import type { Metada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { BlogHero } from "@/components/blog/blog-hero"
import { BlogGrid } from "@/components/blog/blog-grid"
import { BlogCategories } from "@/components/blog/blog-categories"
import { BlogNewsletter } from "@/components/blog/blog-newsletter"

export const metadata: Metadata = {
  title: "SEO Blog - Expert Tips & Strategies",
  description: "Stay updated with the latest SEO trends, link building strategies, and digital marketing insights. Expert tips to improve your website's search rankings and online visibility.",
  keywords: [
    "SEO blog",
    "link building tips",
    "digital marketing blog",
    "SEO strategies",
    "search engine optimization",
    "backlink analysis",
    "SERP tracking",
    "SEO news"
  ],
  openGraph: {
    title: "SEO Blog - Expert Tips & Strategies | LinkNest",
    description: "Stay updated with the latest SEO trends, link building strategies, and digital marketing insights from LinkNest experts.",
    url: "https://linknest.com/blog",
    type: "website",
  },
  twitter: {
    title: "SEO Blog - Expert Tips & Strategies | LinkNest",
    description: "Stay updated with the latest SEO trends, link building strategies, and digital marketing insights from LinkNest experts.",
  },
}

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-slate-950 overflow-x-hidden">
      <Header />
      <main id="main-content" className="focus:outline-none" tabIndex={-1}>
        <BlogHero />
        <BlogCategories />
        <BlogGrid />
        <BlogNewsletter />
      </main>
      <Footer />
    </div>
  )
}
