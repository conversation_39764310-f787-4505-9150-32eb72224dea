import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON>er } from "@/components/footer"
import { ContactHero } from "@/components/contact/contact-hero"
import { ContactForm } from "@/components/contact/contact-form"
import { ContactInfo } from "@/components/contact/contact-info"
import { ContactFAQ } from "@/components/contact/contact-faq"

export const metadata: Metadata = {
  title: "Contact LinkNest - Get Expert SEO Support",
  description: "Get in touch with LinkNest's SEO experts. Contact us for support, partnerships, or to learn how our link analysis tools can boost your website's performance.",
  keywords: [
    "contact LinkNest",
    "SEO support",
    "customer service",
    "link analysis help",
    "SEO consultation",
    "technical support"
  ],
  openGraph: {
    title: "Contact LinkNest - Get Expert SEO Support",
    description: "Get in touch with LinkNest's SEO experts for support, partnerships, or consultations.",
    url: "https://linknest.com/contact",
    type: "website",
  },
  twitter: {
    title: "Contact LinkNest - Get Expert SEO Support",
    description: "Get in touch with LinkNest's SEO experts for support, partnerships, or consultations.",
  },
}

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-slate-950 overflow-x-hidden">
      <Header />
      <main id="main-content" className="focus:outline-none" tabIndex={-1}>
        <ContactHero />
        <div className="grid lg:grid-cols-2 gap-0">
          <ContactForm />
          <ContactInfo />
        </div>
        <ContactFAQ />
      </main>
      <Footer />
    </div>
  )
}
