"use client"

import { useState, useEffect } from 'react'
import { Check, X, AlertCircle } from 'lucide-react'

interface SEOCheckItem {
  id: string
  title: string
  description: string
  status: 'pass' | 'fail' | 'warning'
  check: () => boolean | 'warning'
}

export function SEOChecklist() {
  const [checks, setChecks] = useState<SEOCheckItem[]>([])

  useEffect(() => {
    const seoChecks: SEOCheckItem[] = [
      {
        id: 'title',
        title: 'Page Title',
        description: 'Page has a descriptive title tag',
        status: 'pass',
        check: () => {
          const title = document.title
          return title && title.length > 10 && title.length < 60
        }
      },
      {
        id: 'meta-description',
        title: 'Meta Description',
        description: 'Page has a meta description',
        status: 'pass',
        check: () => {
          const metaDesc = document.querySelector('meta[name="description"]')?.getAttribute('content')
          return metaDesc && metaDesc.length > 120 && metaDesc.length < 160
        }
      },
      {
        id: 'h1',
        title: 'H1 Tag',
        description: 'Page has exactly one H1 tag',
        status: 'pass',
        check: () => {
          const h1Tags = document.querySelectorAll('h1')
          return h1Tags.length === 1
        }
      },
      {
        id: 'images-alt',
        title: 'Image Alt Text',
        description: 'All images have alt attributes',
        status: 'pass',
        check: () => {
          const images = document.querySelectorAll('img')
          const imagesWithoutAlt = Array.from(images).filter(img => !img.getAttribute('alt'))
          return imagesWithoutAlt.length === 0
        }
      },
      {
        id: 'robots',
        title: 'Robots.txt',
        description: 'Robots.txt file exists',
        status: 'pass',
        check: () => {
          // This would need to be checked server-side in a real implementation
          return true
        }
      },
      {
        id: 'sitemap',
        title: 'XML Sitemap',
        description: 'XML sitemap exists and is accessible',
        status: 'pass',
        check: () => {
          // This would need to be checked server-side in a real implementation
          return true
        }
      },
      {
        id: 'structured-data',
        title: 'Structured Data',
        description: 'Page includes structured data markup',
        status: 'pass',
        check: () => {
          const structuredData = document.querySelectorAll('script[type="application/ld+json"]')
          return structuredData.length > 0
        }
      },
      {
        id: 'open-graph',
        title: 'Open Graph Tags',
        description: 'Page includes Open Graph meta tags',
        status: 'pass',
        check: () => {
          const ogTitle = document.querySelector('meta[property="og:title"]')
          const ogDesc = document.querySelector('meta[property="og:description"]')
          return ogTitle && ogDesc
        }
      }
    ]

    // Run checks
    const updatedChecks = seoChecks.map(check => {
      try {
        const result = check.check()
        return {
          ...check,
          status: result === true ? 'pass' : result === 'warning' ? 'warning' : 'fail'
        }
      } catch (error) {
        return { ...check, status: 'fail' as const }
      }
    })

    setChecks(updatedChecks)
  }, [])

  const getIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <Check className="w-5 h-5 text-green-500" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      case 'fail':
        return <X className="w-5 h-5 text-red-500" />
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />
    }
  }

  const passCount = checks.filter(check => check.status === 'pass').length
  const totalChecks = checks.length

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-sm z-50">
      <h3 className="font-semibold text-gray-900 mb-2">
        SEO Checklist ({passCount}/{totalChecks})
      </h3>
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {checks.map(check => (
          <div key={check.id} className="flex items-start gap-2">
            {getIcon(check.status)}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900">{check.title}</p>
              <p className="text-xs text-gray-500">{check.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
