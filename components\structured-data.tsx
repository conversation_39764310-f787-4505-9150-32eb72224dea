import Script from 'next/script'

interface StructuredDataProps {
  data: object
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  )
}

// Organization Schema
export const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "LinkNest",
  "description": "Advanced Link Analysis & SEO Tools",
  "url": "https://linknest.com",
  "logo": "https://linknest.com/logo.png",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "******-123-4567",
    "contactType": "customer service",
    "availableLanguage": "English"
  },
  "sameAs": [
    "https://twitter.com/linknest",
    "https://linkedin.com/company/linknest",
    "https://facebook.com/linknest"
  ]
}

// Website Schema
export const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "LinkNest",
  "url": "https://linknest.com",
  "description": "Powerful link analysis and SEO optimization tools to boost your website's performance and search rankings.",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": "https://linknest.com/search?q={search_term_string}"
    },
    "query-input": "required name=search_term_string"
  }
}

// Software Application Schema
export const softwareSchema = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "LinkNest",
  "description": "Advanced Link Analysis & SEO Tools",
  "url": "https://linknest.com",
  "applicationCategory": "BusinessApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "ratingCount": "1250",
    "bestRating": "5",
    "worstRating": "1"
  }
}
