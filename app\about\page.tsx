import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { AboutHero } from "@/components/about/about-hero"
import { AboutMission } from "@/components/about/about-mission"
import { AboutTeam } from "@/components/about/about-team"
import { AboutValues } from "@/components/about/about-values"
import { AboutStats } from "@/components/about/about-stats"

export const metadata: Metadata = {
  title: "About LinkNest - Our Mission & Team",
  description: "Learn about LinkNest's mission to revolutionize SEO and link analysis. Meet our expert team dedicated to helping businesses improve their search rankings and online presence.",
  keywords: [
    "about LinkNest",
    "SEO company",
    "link analysis team",
    "digital marketing experts",
    "SEO mission",
    "company values"
  ],
  openGraph: {
    title: "About LinkNest - Our Mission & Team",
    description: "Learn about LinkNest's mission to revolutionize SEO and link analysis. Meet our expert team dedicated to helping businesses improve their search rankings.",
    url: "https://linknest.com/about",
    type: "website",
  },
  twitter: {
    title: "About LinkNest - Our Mission & Team",
    description: "Learn about LinkNest's mission to revolutionize SEO and link analysis. Meet our expert team dedicated to helping businesses improve their search rankings.",
  },
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-slate-950 overflow-x-hidden">
      <Header />
      <main id="main-content" className="focus:outline-none" tabIndex={-1}>
        <AboutHero />
        <AboutMission />
        <AboutValues />
        <AboutTeam />
        <AboutStats />
      </main>
      <Footer />
    </div>
  )
}
