import { Offers, ReturnPolicySeasonalOverrides } from 'src/types';
export declare function setOffers(offers?: Offers | Offers[]): {
    hasMerchantReturnPolicy?: {
        returnPolicySeasonalOverride?: ReturnPolicySeasonalOverrides[] | {
            length: number;
            toString(): string;
            toLocaleString(): string;
            pop(): ReturnPolicySeasonalOverrides | undefined;
            push(...items: ReturnPolicySeasonalOverrides[]): number;
            concat(...items: ConcatArray<ReturnPolicySeasonalOverrides>[]): ReturnPolicySeasonalOverrides[];
            concat(...items: (ReturnPolicySeasonalOverrides | ConcatArray<ReturnPolicySeasonalOverrides>)[]): ReturnPolicySeasonalOverrides[];
            join(separator?: string | undefined): string;
            reverse(): ReturnPolicySeasonalOverrides[];
            shift(): ReturnPolicySeasonalOverrides | undefined;
            slice(start?: number | undefined, end?: number | undefined): ReturnPolicySeasonalOverrides[];
            sort(compareFn?: ((a: ReturnPolicySeasonalOverrides, b: ReturnPolicySeasonalOverrides) => number) | undefined): ReturnPolicySeasonalOverrides[];
            splice(start: number, deleteCount?: number | undefined): ReturnPolicySeasonalOverrides[];
            splice(start: number, deleteCount: number, ...items: ReturnPolicySeasonalOverrides[]): ReturnPolicySeasonalOverrides[];
            unshift(...items: ReturnPolicySeasonalOverrides[]): number;
            indexOf(searchElement: ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
            lastIndexOf(searchElement: ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
            every<S extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => value is S, thisArg?: any): this is S[];
            every(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
            some(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
            forEach(callbackfn: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => void, thisArg?: any): void;
            map<U>(callbackfn: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => U, thisArg?: any): U[];
            filter<S_1 extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => value is S_1, thisArg?: any): S_1[];
            filter(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): ReturnPolicySeasonalOverrides[];
            reduce(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduce(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides, initialValue: ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduce<U_1>(callbackfn: (previousValue: U_1, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => U_1, initialValue: U_1): U_1;
            reduceRight(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduceRight(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides, initialValue: ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduceRight<U_2>(callbackfn: (previousValue: U_2, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => U_2, initialValue: U_2): U_2;
            find<S_2 extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, obj: ReturnPolicySeasonalOverrides[]) => value is S_2, thisArg?: any): S_2 | undefined;
            find(predicate: (value: ReturnPolicySeasonalOverrides, index: number, obj: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): ReturnPolicySeasonalOverrides | undefined;
            findIndex(predicate: (value: ReturnPolicySeasonalOverrides, index: number, obj: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
            fill(value: ReturnPolicySeasonalOverrides, start?: number | undefined, end?: number | undefined): ReturnPolicySeasonalOverrides[];
            copyWithin(target: number, start?: number | undefined, end?: number | undefined): ReturnPolicySeasonalOverrides[];
            entries(): IterableIterator<[number, ReturnPolicySeasonalOverrides]>;
            keys(): IterableIterator<number>;
            values(): IterableIterator<ReturnPolicySeasonalOverrides>;
            includes(searchElement: ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): boolean;
            flatMap<U_3, This = undefined>(callback: (this: This, value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => U_3 | readonly U_3[], thisArg?: This | undefined): U_3[];
            flat<A, D extends number = 1>(this: A, depth?: D | undefined): FlatArray<A, D>[];
            at(index: number): ReturnPolicySeasonalOverrides | undefined;
            findLast<S_3 extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => value is S_3, thisArg?: any): S_3 | undefined;
            findLast(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): ReturnPolicySeasonalOverrides | undefined;
            findLastIndex(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
            [Symbol.iterator](): IterableIterator<ReturnPolicySeasonalOverrides>;
            [Symbol.unscopables]: {
                [x: number]: boolean | undefined;
                length?: boolean | undefined;
                toString?: boolean | undefined;
                toLocaleString?: boolean | undefined;
                pop?: boolean | undefined;
                push?: boolean | undefined;
                concat?: boolean | undefined;
                join?: boolean | undefined;
                reverse?: boolean | undefined;
                shift?: boolean | undefined;
                slice?: boolean | undefined;
                sort?: boolean | undefined;
                splice?: boolean | undefined;
                unshift?: boolean | undefined;
                indexOf?: boolean | undefined;
                lastIndexOf?: boolean | undefined;
                every?: boolean | undefined;
                some?: boolean | undefined;
                forEach?: boolean | undefined;
                map?: boolean | undefined;
                filter?: boolean | undefined;
                reduce?: boolean | undefined;
                reduceRight?: boolean | undefined;
                find?: boolean | undefined;
                findIndex?: boolean | undefined;
                fill?: boolean | undefined;
                copyWithin?: boolean | undefined;
                entries?: boolean | undefined;
                keys?: boolean | undefined;
                values?: boolean | undefined;
                includes?: boolean | undefined;
                flatMap?: boolean | undefined;
                flat?: boolean | undefined;
                at?: boolean | undefined;
                findLast?: boolean | undefined;
                findLastIndex?: boolean | undefined;
                [Symbol.iterator]?: boolean | undefined;
                readonly [Symbol.unscopables]?: boolean | undefined;
            };
            '@type': string;
        } | undefined;
        returnPolicyCategory?: import("src/types").ReturnPolicyCategory | undefined;
        returnMethod?: import("src/types").ReturnMethod | import("src/types").ReturnMethod[] | undefined;
        returnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
        refundType?: import("src/types").RefundType | import("src/types").RefundType[] | undefined;
        applicableCountry?: string | string[] | undefined;
        returnPolicyCountry?: string | string[] | undefined;
        merchantReturnLink?: string | undefined;
        itemCondition?: import("src/types").ItemCondition | import("src/types").ItemCondition[] | undefined;
        inStoreReturnsOffered?: boolean | undefined;
        restockingFee?: string | number | undefined;
        returnShippingFeesAmount?: {
            value: number;
            currency: string;
        } | undefined;
        customerRemorseReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
        customerRemorseReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
        itemDefectReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
        itemDefectReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
        '@type': string;
    } | undefined;
    seller: {
        '@type': string;
        name: string;
    };
    '@type': string;
    price: string;
    priceCurrency: string;
    priceValidUntil?: string | undefined;
    itemCondition?: string | undefined;
    availability?: string | undefined;
    url?: string | undefined;
    validFrom?: string | undefined;
} | {
    hasMerchantReturnPolicy?: {
        returnPolicySeasonalOverride?: ReturnPolicySeasonalOverrides[] | {
            length: number;
            toString(): string;
            toLocaleString(): string;
            pop(): ReturnPolicySeasonalOverrides | undefined;
            push(...items: ReturnPolicySeasonalOverrides[]): number;
            concat(...items: ConcatArray<ReturnPolicySeasonalOverrides>[]): ReturnPolicySeasonalOverrides[];
            concat(...items: (ReturnPolicySeasonalOverrides | ConcatArray<ReturnPolicySeasonalOverrides>)[]): ReturnPolicySeasonalOverrides[];
            join(separator?: string | undefined): string;
            reverse(): ReturnPolicySeasonalOverrides[];
            shift(): ReturnPolicySeasonalOverrides | undefined;
            slice(start?: number | undefined, end?: number | undefined): ReturnPolicySeasonalOverrides[];
            sort(compareFn?: ((a: ReturnPolicySeasonalOverrides, b: ReturnPolicySeasonalOverrides) => number) | undefined): ReturnPolicySeasonalOverrides[];
            splice(start: number, deleteCount?: number | undefined): ReturnPolicySeasonalOverrides[];
            splice(start: number, deleteCount: number, ...items: ReturnPolicySeasonalOverrides[]): ReturnPolicySeasonalOverrides[];
            unshift(...items: ReturnPolicySeasonalOverrides[]): number;
            indexOf(searchElement: ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
            lastIndexOf(searchElement: ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
            every<S extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => value is S, thisArg?: any): this is S[];
            every(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
            some(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
            forEach(callbackfn: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => void, thisArg?: any): void;
            map<U>(callbackfn: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => U, thisArg?: any): U[];
            filter<S_1 extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => value is S_1, thisArg?: any): S_1[];
            filter(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): ReturnPolicySeasonalOverrides[];
            reduce(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduce(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides, initialValue: ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduce<U_1>(callbackfn: (previousValue: U_1, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => U_1, initialValue: U_1): U_1;
            reduceRight(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduceRight(callbackfn: (previousValue: ReturnPolicySeasonalOverrides, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => ReturnPolicySeasonalOverrides, initialValue: ReturnPolicySeasonalOverrides): ReturnPolicySeasonalOverrides;
            reduceRight<U_2>(callbackfn: (previousValue: U_2, currentValue: ReturnPolicySeasonalOverrides, currentIndex: number, array: ReturnPolicySeasonalOverrides[]) => U_2, initialValue: U_2): U_2;
            find<S_2 extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, obj: ReturnPolicySeasonalOverrides[]) => value is S_2, thisArg?: any): S_2 | undefined;
            find(predicate: (value: ReturnPolicySeasonalOverrides, index: number, obj: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): ReturnPolicySeasonalOverrides | undefined;
            findIndex(predicate: (value: ReturnPolicySeasonalOverrides, index: number, obj: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
            fill(value: ReturnPolicySeasonalOverrides, start?: number | undefined, end?: number | undefined): ReturnPolicySeasonalOverrides[];
            copyWithin(target: number, start?: number | undefined, end?: number | undefined): ReturnPolicySeasonalOverrides[];
            entries(): IterableIterator<[number, ReturnPolicySeasonalOverrides]>;
            keys(): IterableIterator<number>;
            values(): IterableIterator<ReturnPolicySeasonalOverrides>;
            includes(searchElement: ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): boolean;
            flatMap<U_3, This = undefined>(callback: (this: This, value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => U_3 | readonly U_3[], thisArg?: This | undefined): U_3[];
            flat<A, D extends number = 1>(this: A, depth?: D | undefined): FlatArray<A, D>[];
            at(index: number): ReturnPolicySeasonalOverrides | undefined;
            findLast<S_3 extends ReturnPolicySeasonalOverrides>(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => value is S_3, thisArg?: any): S_3 | undefined;
            findLast(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): ReturnPolicySeasonalOverrides | undefined;
            findLastIndex(predicate: (value: ReturnPolicySeasonalOverrides, index: number, array: ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
            [Symbol.iterator](): IterableIterator<ReturnPolicySeasonalOverrides>;
            [Symbol.unscopables]: {
                [x: number]: boolean | undefined;
                length?: boolean | undefined;
                toString?: boolean | undefined;
                toLocaleString?: boolean | undefined;
                pop?: boolean | undefined;
                push?: boolean | undefined;
                concat?: boolean | undefined;
                join?: boolean | undefined;
                reverse?: boolean | undefined;
                shift?: boolean | undefined;
                slice?: boolean | undefined;
                sort?: boolean | undefined;
                splice?: boolean | undefined;
                unshift?: boolean | undefined;
                indexOf?: boolean | undefined;
                lastIndexOf?: boolean | undefined;
                every?: boolean | undefined;
                some?: boolean | undefined;
                forEach?: boolean | undefined;
                map?: boolean | undefined;
                filter?: boolean | undefined;
                reduce?: boolean | undefined;
                reduceRight?: boolean | undefined;
                find?: boolean | undefined;
                findIndex?: boolean | undefined;
                fill?: boolean | undefined;
                copyWithin?: boolean | undefined;
                entries?: boolean | undefined;
                keys?: boolean | undefined;
                values?: boolean | undefined;
                includes?: boolean | undefined;
                flatMap?: boolean | undefined;
                flat?: boolean | undefined;
                at?: boolean | undefined;
                findLast?: boolean | undefined;
                findLastIndex?: boolean | undefined;
                [Symbol.iterator]?: boolean | undefined;
                readonly [Symbol.unscopables]?: boolean | undefined;
            };
            '@type': string;
        } | undefined;
        returnPolicyCategory?: import("src/types").ReturnPolicyCategory | undefined;
        returnMethod?: import("src/types").ReturnMethod | import("src/types").ReturnMethod[] | undefined;
        returnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
        refundType?: import("src/types").RefundType | import("src/types").RefundType[] | undefined;
        applicableCountry?: string | string[] | undefined;
        returnPolicyCountry?: string | string[] | undefined;
        merchantReturnLink?: string | undefined;
        itemCondition?: import("src/types").ItemCondition | import("src/types").ItemCondition[] | undefined;
        inStoreReturnsOffered?: boolean | undefined;
        restockingFee?: string | number | undefined;
        returnShippingFeesAmount?: {
            value: number;
            currency: string;
        } | undefined;
        customerRemorseReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
        customerRemorseReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
        itemDefectReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
        itemDefectReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
        '@type': string;
    } | undefined;
    seller: {
        '@type': string;
        name: string;
    };
    '@type': string;
    price: string;
    priceCurrency: string;
    priceValidUntil?: string | undefined;
    itemCondition?: string | undefined;
    availability?: string | undefined;
    url?: string | undefined;
    validFrom?: string | undefined;
}[] | undefined;
