import type { Review } from 'src/types';
export declare function setReviews(reviews?: Review | Review[]): {
    publisher?: {
        '@type': string;
        name: string;
        logo: {
            '@type': string;
            url: string;
        } | undefined;
    } | undefined;
    reviewRating: {
        '@type': string;
        bestRating?: string | undefined;
        ratingValue: string;
        worstRating?: string | undefined;
    } | undefined;
    author?: {
        '@type': string;
        name: string;
        url?: undefined;
    } | {
        '@type': "Person" | "Organization";
        name: string;
        url: string | undefined;
    } | ({
        '@type': string;
        name: string;
        url?: undefined;
    } | {
        '@type': "Person" | "Organization";
        name: string;
        url: string | undefined;
    } | undefined)[] | undefined;
    '@type': string;
    datePublished?: string | undefined;
    reviewBody?: string | undefined;
    name?: string | undefined;
} | {
    publisher?: {
        '@type': string;
        name: string;
        logo: {
            '@type': string;
            url: string;
        } | undefined;
    } | undefined;
    reviewRating: {
        '@type': string;
        bestRating?: string | undefined;
        ratingValue: string;
        worstRating?: string | undefined;
    } | undefined;
    author?: {
        '@type': string;
        name: string;
        url?: undefined;
    } | {
        '@type': "Person" | "Organization";
        name: string;
        url: string | undefined;
    } | ({
        '@type': string;
        name: string;
        url?: undefined;
    } | {
        '@type': "Person" | "Organization";
        name: string;
        url: string | undefined;
    } | undefined)[] | undefined;
    '@type': string;
    datePublished?: string | undefined;
    reviewBody?: string | undefined;
    name?: string | undefined;
}[] | undefined;
