import { ImageResponse } from 'next/og'

// Route segment config
export const runtime = 'edge'

// Image metadata
export const alt = 'LinkNest - Advanced Link Analysis & SEO Tools'
export const size = {
  width: 1200,
  height: 630,
}
export const contentType = 'image/png'

// Image generation
export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#0f172a',
          backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '40px',
            textAlign: 'center',
          }}
        >
          <h1
            style={{
              fontSize: '72px',
              fontWeight: 'bold',
              color: 'white',
              marginBottom: '20px',
              textShadow: '2px 2px 4px rgba(0,0,0,0.3)',
            }}
          >
            LinkNest
          </h1>
          <p
            style={{
              fontSize: '32px',
              color: '#e2e8f0',
              marginBottom: '30px',
              maxWidth: '800px',
              lineHeight: 1.4,
            }}
          >
            Advanced Link Analysis & SEO Tools
          </p>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: 'rgba(255,255,255,0.1)',
              padding: '15px 30px',
              borderRadius: '50px',
              border: '2px solid rgba(255,255,255,0.2)',
            }}
          >
            <span
              style={{
                fontSize: '24px',
                color: '#cbd5e1',
                fontWeight: '500',
              }}
            >
              Boost Your SEO Performance
            </span>
          </div>
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
