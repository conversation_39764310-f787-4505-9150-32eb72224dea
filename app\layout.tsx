import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from 'next/font/google'
import "./globals.css"
import { PerformanceMonitor } from "@/components/performance-monitor"
import { SkipLink } from "@/components/ui/skip-link"
import { SEOChecklist } from "@/components/seo-checklist"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  metadataBase: new URL('https://linknest.com'),
  title: {
    default: "LinkNest - Advanced Link Analysis & SEO Tools",
    template: "%s | LinkNest"
  },
  description: "Powerful link analysis and SEO optimization tools to boost your website's performance and search rankings. Discover hidden link opportunities, track competitors, and optimize your SEO strategy.",
  keywords: [
    "SEO tools",
    "link analysis",
    "backlink checker",
    "competitor analysis",
    "link building",
    "website optimization",
    "search engine optimization",
    "digital marketing",
    "SERP tracking",
    "link monitoring"
  ],
  authors: [{ name: "LinkNest Team" }],
  creator: "LinkNest",
  publisher: "LinkNest",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://linknest.com',
    title: 'LinkNest - Advanced Link Analysis & SEO Tools',
    description: 'Powerful link analysis and SEO optimization tools to boost your website\'s performance and search rankings.',
    siteName: 'LinkNest',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'LinkNest - Advanced Link Analysis & SEO Tools',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'LinkNest - Advanced Link Analysis & SEO Tools',
    description: 'Powerful link analysis and SEO optimization tools to boost your website\'s performance and search rankings.',
    images: ['/og-image.png'],
    creator: '@linknest',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SkipLink href="#main-content">Skip to main content</SkipLink>
        <SkipLink href="#navigation">Skip to navigation</SkipLink>
        <PerformanceMonitor />
        <SEOChecklist />
        {children}
      </body>
    </html>
  )
}
