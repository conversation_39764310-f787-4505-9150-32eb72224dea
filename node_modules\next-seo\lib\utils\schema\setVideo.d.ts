import type { Video } from 'src/types';
export declare function setVideo(video?: Video, setContext?: boolean): {
    thumbnailUrl: string[];
    hasPart: {
        '@type': string;
        name: string;
        startOffset: number;
        url: string;
    } | {
        '@type': string;
        name: string;
        startOffset: number;
        url: string;
    }[] | undefined;
    interactionStatistic: {
        '@type': string;
        interactionType: string;
        userInteractionCount: number;
    } | undefined;
    publication: {
        '@type': string;
        name?: string | undefined;
        isLiveBroadcast?: boolean | undefined;
        startDate?: string | undefined;
        endDate?: string | undefined;
    } | {
        '@type': string;
        name?: string | undefined;
        isLiveBroadcast?: boolean | undefined;
        startDate?: string | undefined;
        endDate?: string | undefined;
    }[] | undefined;
    '@context'?: string | undefined;
    '@type': string;
    name: string;
    description: string;
    uploadDate: string;
    contentUrl?: string | undefined;
    duration?: string | undefined;
    embedUrl?: string | undefined;
    expires?: string | undefined;
    regionsAllowed?: string | string[] | undefined;
} | undefined;
