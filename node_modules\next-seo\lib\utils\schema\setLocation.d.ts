import { Location } from 'src/types';
export declare function setLocation(location?: Location): string | {
    '@type': string;
    name?: string | undefined;
    sameAs?: string | undefined;
    url: string;
} | {
    address: string | {
        streetAddress: string;
        addressLocality: string;
        addressRegion?: string | undefined;
        postalCode: string;
        addressCountry: string;
        '@type': string;
    } | (string | {
        streetAddress: string;
        addressLocality: string;
        addressRegion?: string | undefined;
        postalCode: string;
        addressCountry: string;
        '@type': string;
    })[] | undefined;
    '@type': string;
    name: string;
    sameAs?: string | undefined;
} | undefined;
