"use client"

import { useEffect } from 'react'

export function PerformanceMonitor() {
  useEffect(() => {
    // Web Vitals monitoring
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Monitor Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          // Log performance metrics (in production, send to analytics)
          if (process.env.NODE_ENV === 'development') {
            console.log(`${entry.name}: ${entry.value}`)
          }
          
          // You can send these metrics to your analytics service
          // Example: analytics.track(entry.name, { value: entry.value })
        }
      })

      // Observe different performance metrics
      try {
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
        observer.observe({ entryTypes: ['first-input'] })
        observer.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        // Some browsers might not support all entry types
        console.warn('Performance observer not fully supported')
      }

      // Cleanup
      return () => {
        observer.disconnect()
      }
    }
  }, [])

  return null
}

// Utility function to get Core Web Vitals
export function getCoreWebVitals() {
  if (typeof window === 'undefined') return null

  return new Promise((resolve) => {
    const vitals = {
      lcp: 0,
      fid: 0,
      cls: 0,
    }

    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'largest-contentful-paint':
            vitals.lcp = entry.startTime
            break
          case 'first-input':
            vitals.fid = entry.processingStart - entry.startTime
            break
          case 'layout-shift':
            if (!entry.hadRecentInput) {
              vitals.cls += entry.value
            }
            break
        }
      }
    })

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
      observer.observe({ entryTypes: ['first-input'] })
      observer.observe({ entryTypes: ['layout-shift'] })
      
      // Resolve after a short delay to collect metrics
      setTimeout(() => {
        observer.disconnect()
        resolve(vitals)
      }, 3000)
    } catch (e) {
      resolve(null)
    }
  })
}
