"use strict";exports.id=474,exports.ids=[474],exports.modules={195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let r=n(740)._(n(6715)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},334:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return h},bumpPrefetchTask:function(){return u},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return a},navigate:function(){return i},prefetch:function(){return r},revalidateEntireCache:function(){return o},schedulePrefetchTask:function(){return s}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,i=n,o=n,a=n,s=n,l=n,u=n,c=n;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),h=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],o=Array.isArray(t),a=o?t[1]:t;!(!a||a.startsWith(i.PAGE_SEGMENT_KEY))&&(o&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):o&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(2859),i=n(3913),o=n(4077),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===i.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(n)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)o.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let n=u(t);void 0!==n&&o.push(n)}return l(o)}function c(e,t){let n=function e(t,n){let[i,a]=t,[l,c]=n,d=s(i),h=s(l);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||h.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var f;return null!=(f=u(n))?f:""}for(let t in a)if(c[t]){let n=e(a[t],c[t]);if(null!==n)return s(l)+"/"+n}return null}(e,t);return null==n||"/"===n?n:l(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1012:(e,t,n)=>{let r;n.d(t,{P:()=>oR});var i=n(3210);let o=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],a=new Set(o),s=e=>180*e/Math.PI,l=e=>c(s(Math.atan2(e[1],e[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:l,rotateZ:l,skewX:e=>s(Math.atan(e[1])),skewY:e=>s(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},c=e=>((e%=360)<0&&(e+=360),e),d=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),h=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),f={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:h,scale:e=>(d(e)+h(e))/2,rotateX:e=>c(s(Math.atan2(e[6],e[5]))),rotateY:e=>c(s(Math.atan2(-e[2],e[0]))),rotateZ:l,rotate:l,skewX:e=>s(Math.atan(e[4])),skewY:e=>s(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function p(e){return+!!e.includes("scale")}function m(e,t){let n,r;if(!e||"none"===e)return p(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=f,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=u,r=t}if(!r)return p(t);let o=n[t],a=r[1].split(",").map(y);return"function"==typeof o?o(a):a[o]}let g=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return m(n,t)};function y(e){return parseFloat(e.trim())}let v=e=>t=>"string"==typeof t&&t.startsWith(e),b=v("--"),x=v("var(--"),P=e=>!!x(e)&&w.test(e.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function T({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}let R=(e,t,n)=>e+(t-e)*n;function S(e){return void 0===e||1===e}function E({scale:e,scaleX:t,scaleY:n}){return!S(e)||!S(t)||!S(n)}function M(e){return E(e)||_(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function _(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function j(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function A(e,t=0,n=1,r,i){e.min=j(e.min,t,n,r,i),e.max=j(e.max,t,n,r,i)}function C(e,{x:t,y:n}){A(e.x,t.translate,t.scale,t.originPoint),A(e.y,n.translate,n.scale,n.originPoint)}function O(e,t){e.min=e.min+t,e.max=e.max+t}function k(e,t,n,r,i=.5){let o=R(e.min,e.max,i);A(e,t,n,o,r)}function D(e,t){k(e.x,t.x,t.scaleX,t.scale,t.originX),k(e.y,t.y,t.scaleY,t.scale,t.originY)}function L(e,t){return T(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let V=new Set(["width","height","top","left","right","bottom",...o]),N=(e,t,n)=>n>t?t:n<e?e:n,U={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},F={...U,transform:e=>N(0,1,e)},I={...U,default:1},B=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),z=B("deg"),H=B("%"),W=B("px"),K=B("vh"),$=B("vw"),G={...H,parse:e=>H.parse(e)/100,transform:e=>H.transform(100*e)},Y=e=>t=>t.test(e),X=[U,W,H,z,$,K,{test:e=>"auto"===e,parse:e=>e}],q=e=>X.find(Y(e)),Z=()=>{},J=()=>{},Q=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),ee=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,et=e=>e===U||e===W,en=new Set(["x","y","z"]),er=o.filter(e=>!en.has(e)),ei={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>m(t,"x"),y:(e,{transform:t})=>m(t,"y")};ei.translateX=ei.x,ei.translateY=ei.y;let eo=e=>e,ea={},es=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],el={value:null,addProjectionMetrics:null};function eu(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,a=es.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,a=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(c.schedule(t),e()),l++,t(s)}let c={schedule:(e,t=!1,o=!1)=>{let s=o&&i?n:r;return t&&a.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(s=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&el.value&&el.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:h,render:f,postRender:p}=a,m=()=>{let o=ea.useManualTiming?i.timestamp:performance.now();n=!1,ea.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),c.process(i),d.process(i),h.process(i),f.process(i),p.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(m))},g=()=>{n=!0,r=!0,i.isProcessing||e(m)};return{schedule:es.reduce((e,t)=>{let r=a[t];return e[t]=(e,t=!1,i=!1)=>(n||g(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<es.length;t++)a[es[t]].cancel(e)},state:i,steps:a}}let{schedule:ec,cancel:ed,state:eh,steps:ef}=eu("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:eo,!0),ep=new Set,em=!1,eg=!1,ey=!1;function ev(){if(eg){let e=Array.from(ep).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return er.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}eg=!1,em=!1,ep.forEach(e=>e.complete(ey)),ep.clear()}function eb(){ep.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(eg=!0)})}class ex{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(ep.add(this),em||(em=!0,ec.read(eb),ec.resolveKeyframes(ev))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}!function(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ep.delete(this)}cancel(){"scheduled"===this.state&&(ep.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eP=e=>/^0[^.\s]+$/u.test(e),ew=e=>Math.round(1e5*e)/1e5,eT=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eR=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eS=(e,t)=>n=>!!("string"==typeof n&&eR.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),eE=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,a,s]=r.match(eT);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},eM=e=>N(0,255,e),e_={...U,transform:e=>Math.round(eM(e))},ej={test:eS("rgb","red"),parse:eE("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+e_.transform(e)+", "+e_.transform(t)+", "+e_.transform(n)+", "+ew(F.transform(r))+")"},eA={test:eS("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ej.transform},eC={test:eS("hsl","hue"),parse:eE("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+H.transform(ew(t))+", "+H.transform(ew(n))+", "+ew(F.transform(r))+")"},eO={test:e=>ej.test(e)||eA.test(e)||eC.test(e),parse:e=>ej.test(e)?ej.parse(e):eC.test(e)?eC.parse(e):eA.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ej.transform(e):eC.transform(e),getAnimatableNone:e=>{let t=eO.parse(e);return t.alpha=0,eO.transform(t)}},ek=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eD="number",eL="color",eV=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eN(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,a=t.replace(eV,e=>(eO.test(e)?(r.color.push(o),i.push(eL),n.push(eO.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eD),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:a,indexes:r,types:i}}function eU(e){return eN(e).values}function eF(e){let{split:t,types:n}=eN(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eD?i+=ew(e[o]):t===eL?i+=eO.transform(e[o]):i+=e[o]}return i}}let eI=e=>"number"==typeof e?0:eO.test(e)?eO.getAnimatableNone(e):e,eB={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(eT)?.length||0)+(e.match(ek)?.length||0)>0},parse:eU,createTransformer:eF,getAnimatableNone:function(e){let t=eU(e);return eF(e)(t.map(eI))}},ez=new Set(["brightness","contrast","saturate","opacity"]);function eH(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(eT)||[];if(!r)return e;let i=n.replace(r,""),o=+!!ez.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let eW=/\b([a-z-]*)\(.*?\)/gu,eK={...eB,getAnimatableNone:e=>{let t=e.match(eW);return t?t.map(eH).join(" "):e}},e$={...U,transform:Math.round},eG={borderWidth:W,borderTopWidth:W,borderRightWidth:W,borderBottomWidth:W,borderLeftWidth:W,borderRadius:W,radius:W,borderTopLeftRadius:W,borderTopRightRadius:W,borderBottomRightRadius:W,borderBottomLeftRadius:W,width:W,maxWidth:W,height:W,maxHeight:W,top:W,right:W,bottom:W,left:W,padding:W,paddingTop:W,paddingRight:W,paddingBottom:W,paddingLeft:W,margin:W,marginTop:W,marginRight:W,marginBottom:W,marginLeft:W,backgroundPositionX:W,backgroundPositionY:W,rotate:z,rotateX:z,rotateY:z,rotateZ:z,scale:I,scaleX:I,scaleY:I,scaleZ:I,skew:z,skewX:z,skewY:z,distance:W,translateX:W,translateY:W,translateZ:W,x:W,y:W,z:W,perspective:W,transformPerspective:W,opacity:F,originX:G,originY:G,originZ:W,zIndex:e$,fillOpacity:F,strokeOpacity:F,numOctaves:e$},eY={...eG,color:eO,backgroundColor:eO,outlineColor:eO,fill:eO,stroke:eO,borderColor:eO,borderTopColor:eO,borderRightColor:eO,borderBottomColor:eO,borderLeftColor:eO,filter:eK,WebkitFilter:eK},eX=e=>eY[e];function eq(e,t){let n=eX(e);return n!==eK&&(n=eB),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let eZ=new Set(["auto","none","0"]);class eJ extends ex{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&P(r=r.trim())){let i=function e(t,n,r=1){J(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[i,o]=function(e){let t=ee.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let a=window.getComputedStyle(n).getPropertyValue(i);if(a){let e=a.trim();return Q(e)?parseFloat(e):e}return P(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!V.has(n)||2!==e.length)return;let[r,i]=e,o=q(r),a=q(i);if(o!==a){if(et(o)&&et(a))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else ei[n]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||eP(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!eZ.has(t)&&eN(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=eq(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ei[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=ei[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let eQ=e=>!!(e&&e.getVelocity);function e0(){r=void 0}let e1={now:()=>(void 0===r&&e1.set(eh.isProcessing||ea.useManualTiming?eh.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(e0)}};function e2(e,t){-1===e.indexOf(t)&&e.push(t)}function e5(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class e3{constructor(){this.subscriptions=[]}add(e){return e2(this.subscriptions,e),()=>e5(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let e4=e=>!isNaN(parseFloat(e)),e9={current:void 0};class e6{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{let t=e1.now();if(this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=e1.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=e4(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e3);let n=this.events[e].add(t);return"change"===e?()=>{n(),ec.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return e9.current&&e9.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=e1.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function e7(e,t){return new e6(e,t)}let e8=[...X,eO,eB],te=e=>e8.find(Y(e)),{schedule:tt}=eu(queueMicrotask,!1),tn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},tr={};for(let e in tn)tr[e]={isEnabled:t=>tn[e].some(e=>!!t[e])};let ti=()=>({translate:0,scale:1,origin:0,originPoint:0}),to=()=>({x:ti(),y:ti()}),ta=()=>({min:0,max:0}),ts=()=>({x:ta(),y:ta()}),tl="undefined"!=typeof window,tu={current:null},tc={current:!1},td=new WeakMap;function th(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function tf(e){return"string"==typeof e||Array.isArray(e)}let tp=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tm=["initial",...tp];function tg(e){return th(e.animate)||tm.some(t=>tf(e[t]))}function ty(e){return!!(tg(e)||e.variants)}function tv(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function tb(e,t,n,r){if("function"==typeof t){let[i,o]=tv(r);t=t(void 0!==n?n:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=tv(r);t=t(void 0!==n?n:e.custom,i,o)}return t}let tx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tP{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ex,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=e1.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,ec.render(this.render,!1,!0))};let{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=tg(t),this.isVariantNode=ty(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==s[e]&&eQ(t)&&t.set(s[e])}}mount(e){this.current=e,td.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),tc.current||function(){if(tc.current=!0,tl){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>tu.current=e.matches;e.addEventListener("change",t),t()}else tu.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tu.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),ed(this.notifyUpdate),ed(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=a.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&ec.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in tr){let t=tr[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ts()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tx.length;t++){let n=tx[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(eQ(i))e.addValue(r,i);else if(eQ(o))e.addValue(r,e7(i,{owner:e}));else if(o!==i){if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,e7(void 0!==t?t:i,{owner:e}))}}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=e7(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(Q(n)||eP(n))?n=parseFloat(n):!te(n)&&eB.test(t)&&(n=eq(e,t)),this.setBaseTarget(e,eQ(n)?n.get():n)),eQ(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=tb(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||eQ(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new e3),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}scheduleRenderMicrotask(){tt.render(this.render)}}class tw extends tP{constructor(){super(...arguments),this.KeyframeResolver=eJ}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eQ(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}let tT=(e,t)=>t&&"number"==typeof e?t.transform(e):e,tR={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tS=o.length;function tE(e,t,n){let{style:r,vars:i,transformOrigin:s}=e,l=!1,u=!1;for(let e in t){let n=t[e];if(a.has(e)){l=!0;continue}if(b(e)){i[e]=n;continue}{let t=tT(n,eG[e]);e.startsWith("origin")?(u=!0,s[e]=t):r[e]=t}}if(!t.transform&&(l||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<tS;a++){let s=o[a],l=e[s];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!s.startsWith("scale"):0===parseFloat(l))||n){let e=tT(l,eG[s]);if(!u){i=!1;let t=tR[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:n=0}=s;r.transformOrigin=`${e} ${t} ${n}`}}function tM(e,{style:t,vars:n},r,i){let o;let a=e.style;for(o in t)a[o]=t[o];for(o in i?.applyProjectionStyles(a,r),n)a.setProperty(o,n[o])}let t_={};function tj(e,{layout:t,layoutId:n}){return a.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!t_[e]||"opacity"===e)}function tA(e,t,n){let{style:r}=e,i={};for(let o in r)(eQ(r[o])||t.style&&eQ(t.style[o])||tj(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}class tC extends tw{constructor(){super(...arguments),this.type="html",this.renderInstance=tM}readValueFromInstance(e,t){if(a.has(t))return this.projection?.isProjecting?p(t):g(e,t);{let n=window.getComputedStyle(e),r=(b(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return L(e,t)}build(e,t,n){tE(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return tA(e,t,n)}}let tO=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tk={offset:"stroke-dashoffset",array:"stroke-dasharray"},tD={offset:"strokeDashoffset",array:"strokeDasharray"};function tL(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:a=0,...s},l,u,c){if(tE(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?tk:tD;e[o.offset]=W.transform(-r);let a=W.transform(t),s=W.transform(n);e[o.array]=`${a} ${s}`}(d,i,o,a,!1)}let tV=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),tN=e=>"string"==typeof e&&"svg"===e.toLowerCase();function tU(e,t,n){let r=tA(e,t,n);for(let n in e)(eQ(e[n])||eQ(t[n]))&&(r[-1!==o.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}class tF extends tw{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ts}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(a.has(t)){let e=eX(t);return e&&e.default||0}return t=tV.has(t)?t:tO(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return tU(e,t,n)}build(e,t,n){tL(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){!function(e,t,n,r){for(let n in tM(e,t,void 0,r),t.attrs)e.setAttribute(tV.has(n)?n:tO(n),t.attrs[n])}(e,t,0,r)}mount(e){this.isSVGTag=tN(e.tagName),super.mount(e)}}let tI=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tB(e){if("string"!=typeof e||e.includes("-"));else if(tI.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var tz=n(687);let tH=(0,i.createContext)({}),tW=(0,i.createContext)({strict:!1}),tK=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),t$=(0,i.createContext)({});function tG(e){return Array.isArray(e)?e.join(" "):e}let tY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tX(e,t,n){for(let r in t)eQ(t[r])||tj(r,n)||(e[r]=t[r])}let tq=()=>({...tY(),attrs:{}}),tZ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tJ(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tZ.has(e)}let tQ=e=>!tJ(e);try{!function(e){"function"==typeof e&&(tQ=t=>t.startsWith("on")?!tJ(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let t0=(0,i.createContext)(null);function t1(e){return eQ(e)?e.get():e}let t2=e=>(t,n)=>{let r=(0,i.useContext)(t$),o=(0,i.useContext)(t0),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,i){return{latestValues:function(e,t,n,r){let i={},o=r(e,{});for(let e in o)i[e]=t1(o[e]);let{initial:a,animate:s}=e,l=tg(e),u=ty(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===s&&(s=t.animate));let c=!!n&&!1===n.initial,d=(c=c||!1===a)?s:a;if(d&&"boolean"!=typeof d&&!th(d)){let t=Array.isArray(d)?d:[d];for(let n=0;n<t.length;n++){let r=tb(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(i[e]=t)}for(let t in e)i[t]=e[t]}}}return i}(n,r,i,e),renderState:t()}})(e,t,r,o);return n?a():function(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}(a)},t5=t2({scrapeMotionValuesFromProps:tA,createRenderState:tY}),t3=t2({scrapeMotionValuesFromProps:tU,createRenderState:tq}),t4=Symbol.for("motionComponentSymbol");function t9(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let t6="data-"+tO("framerAppearId"),t7=(0,i.createContext)({}),t8=tl?i.useLayoutEffect:i.useEffect;function ne(e,{forwardMotionProps:t=!1}={},n,r){n&&function(e){for(let t in e)tr[t]={...tr[t],...e[t]}}(n);let o=tB(e)?t3:t5;function a(n,a){var s,l,u;let c;let d={...(0,i.useContext)(tK),...n,layoutId:function({layoutId:e}){let t=(0,i.useContext)(tH).id;return t&&void 0!==e?t+"-"+e:e}(n)},{isStatic:h}=d,f=function(e){let{initial:t,animate:n}=function(e,t){if(tg(e)){let{initial:t,animate:n}=e;return{initial:!1===t||tf(t)?t:void 0,animate:tf(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(t$));return(0,i.useMemo)(()=>({initial:t,animate:n}),[tG(t),tG(n)])}(n),p=o(n,h);if(!h&&tl){l=0,u=0,(0,i.useContext)(tW).strict;let t=function(e){let{drag:t,layout:n}=tr;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(d);c=t.MeasureLayout,f.visualElement=function(e,t,n,r,o){let{visualElement:a}=(0,i.useContext)(t$),s=(0,i.useContext)(tW),l=(0,i.useContext)(t0),u=(0,i.useContext)(tK).reducedMotion,c=(0,i.useRef)(null);r=r||s.renderer,!c.current&&r&&(c.current=r(e,{visualState:t,parent:a,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=c.current,h=(0,i.useContext)(t7);d&&!d.projection&&o&&("html"===d.type||"svg"===d.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&t9(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(c.current,n,o,h);let f=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{d&&f.current&&d.update(n,l)});let p=n[t6],m=(0,i.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return t8(()=>{d&&(f.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),m.current&&d.animationState&&d.animationState.animateChanges())}),(0,i.useEffect)(()=>{d&&(!m.current&&d.animationState&&d.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1),d.enteringChildren=void 0)}),d}(e,p,d,r,t.ProjectionNode)}return(0,tz.jsxs)(t$.Provider,{value:f,children:[c&&f.visualElement?(0,tz.jsx)(c,{visualElement:f.visualElement,...d}):null,function(e,t,n,{latestValues:r},o,a=!1){let s=(tB(e)?function(e,t,n,r){let o=(0,i.useMemo)(()=>{let n=tq();return tL(n,t,tN(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};tX(t,e.style,e),o.style={...t,...o.style}}return o}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return tX(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,i.useMemo)(()=>{let n=tY();return tE(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(t,r,o,e),l=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(tQ(i)||!0===n&&tJ(i)||!t&&!tJ(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(t,"string"==typeof e,a),u=e!==i.Fragment?{...l,...s,ref:n}:{},{children:c}=t,d=(0,i.useMemo)(()=>eQ(c)?c.get():c,[c]);return(0,i.createElement)(e,{...u,children:d})}(e,n,(s=f.visualElement,(0,i.useCallback)(e=>{e&&p.onMount&&p.onMount(e),s&&(e?s.mount(e):s.unmount()),a&&("function"==typeof a?a(e):t9(a)&&(a.current=e))},[s])),p,h,t)]})}a.displayName=`motion.${"string"==typeof e?e:`create(${e.displayName??e.name??""})`}`;let s=(0,i.forwardRef)(a);return s[t4]=e,s}function nt(e,t,n){let r=e.getProps();return tb(r,t,void 0!==n?n:r.custom,e)}function nn(e,t){return e?.[t]??e?.default??e}let nr=e=>Array.isArray(e);function ni(e,t){let n=e.getValue("willChange");if(eQ(n)&&n.add)return n.add(t);if(!n&&ea.WillChange){let n=new ea.WillChange("auto");e.addValue("willChange",n),n.add(t)}}function no(e){e.duration=0,e.type}let na=(e,t)=>n=>t(e(n)),ns=(...e)=>e.reduce(na),nl=e=>1e3*e,nu=e=>e/1e3,nc={layout:0,mainThread:0,waapi:0};function nd(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function nh(e,t){return n=>n>0?t:e}let nf=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},np=[eA,ej,eC],nm=e=>np.find(t=>t.test(e));function ng(e){let t=nm(e);if(Z(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!t)return!1;let n=t.parse(e);return t===eC&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,a=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,s=2*n-r;i=nd(s,r,e+1/3),o=nd(s,r,e),a=nd(s,r,e-1/3)}else i=o=a=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:r}}(n)),n}let ny=(e,t)=>{let n=ng(e),r=ng(t);if(!n||!r)return nh(e,t);let i={...n};return e=>(i.red=nf(n.red,r.red,e),i.green=nf(n.green,r.green,e),i.blue=nf(n.blue,r.blue,e),i.alpha=R(n.alpha,r.alpha,e),ej.transform(i))},nv=new Set(["none","hidden"]);function nb(e,t){return n=>R(e,t,n)}function nx(e){return"number"==typeof e?nb:"string"==typeof e?P(e)?nh:eO.test(e)?ny:nT:Array.isArray(e)?nP:"object"==typeof e?eO.test(e)?ny:nw:nh}function nP(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>nx(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function nw(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=nx(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let nT=(e,t)=>{let n=eB.createTransformer(t),r=eN(e),i=eN(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?nv.has(e)&&!i.values.length||nv.has(t)&&!r.values.length?function(e,t){return nv.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):ns(nP(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],a=e.indexes[o][r[o]],s=e.values[a]??0;n[i]=s,r[o]++}return n}(r,i),i.values),n):(Z(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),nh(e,t))};function nR(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?R(e,t,n):nx(e)(e,t)}let nS=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>ec.update(t,e),stop:()=>ed(t),now:()=>eh.isProcessing?eh.timestamp:e1.now()}},nE=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function nM(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function n_(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let nj={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function nA(e,t){return e*Math.sqrt(1-t*t)}let nC=["duration","bounce"],nO=["stiffness","damping","mass"];function nk(e,t){return t.some(t=>void 0!==e[t])}function nD(e=nj.visualDuration,t=nj.bounce){let n;let r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,a=r.keyframes[0],s=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:nj.velocity,stiffness:nj.stiffness,damping:nj.damping,mass:nj.mass,isResolvedFromDuration:!1,...e};if(!nk(e,nO)&&nk(e,nC)){if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*N(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:nj.mass,stiffness:r,damping:i}}else{let n=function({duration:e=nj.duration,bounce:t=nj.bounce,velocity:n=nj.velocity,mass:r=nj.mass}){let i,o;Z(e<=nl(nj.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let a=1-t;a=N(nj.minDamping,nj.maxDamping,a),e=N(nj.minDuration,nj.maxDuration,nu(e)),a<1?(i=t=>{let r=t*a,i=r*e;return .001-(r-n)/nA(t,a)*Math.exp(-i)},o=t=>{let r=t*a*e,o=Math.pow(a,2)*Math.pow(t,2)*e,s=Math.exp(-r),l=nA(Math.pow(t,2),a);return(r*n+n-o)*s*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let s=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=nl(e),isNaN(s))return{stiffness:nj.stiffness,damping:nj.damping,duration:e};{let t=Math.pow(s,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:nj.mass}).isResolvedFromDuration=!0}}return t}({...r,velocity:-nu(r.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*d)),y=s-a,v=nu(Math.sqrt(u/d)),b=5>Math.abs(y);if(i||(i=b?nj.restSpeed.granular:nj.restSpeed.default),o||(o=b?nj.restDelta.granular:nj.restDelta.default),g<1){let e=nA(v,g);n=t=>s-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)n=e=>s-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*v*t),r=Math.min(e*t,300);return s-n*((m+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let x={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let r=0===e?m:0;g<1&&(r=0===e?nl(m):n_(n,e,t));let a=Math.abs(s-t)<=o;l.done=Math.abs(r)<=i&&a}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(nM(x),2e4),t=nE(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function nL({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,h;let f=e[0],p={done:!1,value:f},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,g=e=>void 0===s?l:void 0===l?s:Math.abs(s-e)<Math.abs(l-e)?s:l,y=n*t,v=f+y,b=void 0===a?v:a(v);b!==v&&(y=b-f);let x=e=>-y*Math.exp(-e/r),P=e=>b+x(e),w=e=>{let t=x(e),n=P(e);p.done=Math.abs(t)<=u,p.value=p.done?b:n},T=e=>{m(p.value)&&(d=e,h=nD({keyframes:[p.value,g(p.value)],velocity:n_(P,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,w(e),T(e)),void 0!==d&&e>=d)?h.next(e-d):(t||w(e),p)}}}nD.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(nM(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:nu(i)}}(e,100,nD);return e.ease=t.ease,e.duration=nl(t.duration),e.type="keyframes",e};let nV=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function nN(e,t,n,r){if(e===t&&n===r)return eo;let i=t=>(function(e,t,n,r,i){let o,a;let s=0;do(o=nV(a=t+(n-t)/2,r,i)-e)>0?n=a:t=a;while(Math.abs(o)>1e-7&&++s<12);return a})(t,0,1,e,n);return e=>0===e||1===e?e:nV(i(e),t,r)}let nU=nN(.42,0,1,1),nF=nN(0,0,.58,1),nI=nN(.42,0,.58,1),nB=e=>Array.isArray(e)&&"number"!=typeof e[0],nz=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,nH=e=>t=>1-e(1-t),nW=nN(.33,1.53,.69,.99),nK=nH(nW),n$=nz(nK),nG=e=>(e*=2)<1?.5*nK(e):.5*(2-Math.pow(2,-10*(e-1))),nY=e=>1-Math.sin(Math.acos(e)),nX=nH(nY),nq=nz(nY),nZ=e=>Array.isArray(e)&&"number"==typeof e[0],nJ={linear:eo,easeIn:nU,easeInOut:nI,easeOut:nF,circIn:nY,circInOut:nq,circOut:nX,backIn:nK,backInOut:n$,backOut:nW,anticipate:nG},nQ=e=>"string"==typeof e,n0=e=>{if(nZ(e)){J(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,n,r,i]=e;return nN(t,n,r,i)}return nQ(e)?(J(void 0!==nJ[e],`Invalid easing type '${e}'`,"invalid-easing-type"),nJ[e]):e},n1=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function n2({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){let i=nB(r)?r.map(n0):n0(r),o={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(J(o===t.length,"Both input and output ranges must be the same length","range-length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,n){let r=[],i=n||ea.mix||nR,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=ns(Array.isArray(t)?t[n]||eo:t,o)),r.push(o)}return r}(t,r,i),l=s.length,u=n=>{if(a&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=n1(e[r],e[r+1],n);return s[r](i)};return n?t=>u(N(e[0],e[o-1],t)):u}((n&&n.length===t.length?n:function(e){let t=[0];return function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=n1(0,t,r);e.push(R(n,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(i)?i:t.map(()=>i||nI).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=a(t),o.done=t>=e,o)}}let n5=e=>null!==e;function n3(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(n5),a=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return a&&void 0!==r?r:o[a]}let n4={decay:nL,inertia:nL,tween:n2,keyframes:n2,spring:nD};function n9(e){"string"==typeof e.type&&(e.type=n4[e.type])}class n6{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let n7=e=>e/100;class n8 extends n6{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==e1.now()&&this.tick(e1.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},nc.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;n9(e);let{type:t=n2,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:a}=e,s=t||n2;s!==n2&&"number"!=typeof a[0]&&(this.mixKeyframes=ns(n7,nR(a[0],a[1])),a=[0,100]);let l=s({...e,keyframes:a});"mirror"===i&&(this.mirroredGenerator=s({...e,keyframes:[...a].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=nM(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:s}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/a,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/a)):"mirror"===d&&(b=o)),v=N(0,1,n)*a}let x=y?{done:!1,value:u[0]}:b.next(v);i&&(x.value=i(x.value));let{done:P}=x;y||null===s||(P=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return w&&f!==nL&&(x.value=n3(u,this.options,m,this.speed)),p&&p(x.value),w&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return nu(this.calculatedDuration)}get time(){return nu(this.currentTime)}set time(e){e=nl(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(e1.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=nu(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=nS,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(e1.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,nc.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let re=e=>e.startsWith("--");function rt(e){let t;return()=>(void 0===t&&(t=e()),t)}let rn=rt(()=>void 0!==window.ScrollTimeline),rr={},ri=function(e,t){let n=rt(e);return()=>rr[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),ro=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,ra={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ro([0,.65,.55,1]),circOut:ro([.55,0,1,.45]),backIn:ro([.31,.01,.66,-.59]),backOut:ro([.33,1.53,.69,.99])};function rs(e){return"function"==typeof e&&"applyToOptions"in e}class rl extends n6{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:a,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,J("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:e,...t}){return rs(e)&&ri()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?ri()?nE(t,n):"ease-out":nZ(t)?ro(t):Array.isArray(t)?t.map(t=>e(t,n)||ra.easeOut):ra[t]}(s,i);Array.isArray(d)&&(c.easing=d),el.value&&nc.waapi++;let h={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"};u&&(h.pseudoElement=u);let f=e.animate(c,h);return el.value&&f.finished.finally(()=>{nc.waapi--}),f}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=n3(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){re(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return nu(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return nu(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=nl(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&rn())?(this.animation.timeline=e,eo):t(this)}}let ru={anticipate:nG,backInOut:n$,circInOut:nq};class rc extends rl{constructor(e){(function(e){"string"==typeof e.ease&&e.ease in ru&&(e.ease=ru[e.ease])})(e),n9(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e){t.set(e);return}let a=new n8({...o,autoplay:!1}),s=nl(this.finishedTime??this.time);t.setWithVelocity(a.sample(s-10).value,a.sample(s).value,10),a.stop()}}let rd=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eB.test(e)||"0"===e)&&!e.startsWith("url(")),rh=new Set(["opacity","clipPath","filter","transform"]),rf=rt(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class rp extends n6{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:a,name:s,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=e1.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:s,motionValue:l,element:u,...c},h=u?.KeyframeResolver||ex;this.keyframeResolver=new h(a,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),s,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:a,delay:s,isHandoff:l,onUpdate:u}=n;this.resolvedAt=e1.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=rd(i,t),s=rd(o,t);return Z(a===s,`You are trying to animate ${t} from "${i}" to "${o}". "${a?o:i}" is not an animatable value.`,"value-not-animatable"),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||rs(n))&&r)}(e,i,o,a)&&((ea.instantAnimations||!s)&&u?.(n3(e,n,t)),e[0]=e[e.length-1],no(n),n.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},d=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:a}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return rf()&&n&&rh.has(n)&&("transform"!==n||!l)&&!s&&!r&&"mirror"!==i&&0!==o&&"inertia"!==a}(c)?new rc({...c,element:c.motionValue.owner.current}):new n8(c);d.finished.then(()=>this.notifyFinished()).catch(eo),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ey=!0,eb(),ev(),ey=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let rm=e=>null!==e,rg={type:"spring",stiffness:500,damping:25,restSpeed:10},ry=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),rv={type:"keyframes",duration:.8},rb={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rx=(e,{keyframes:t})=>t.length>2?rv:a.has(e)?e.startsWith("scale")?ry(t[1]):rg:rb,rP=(e,t,n,r={},i,o)=>a=>{let s=nn(r,e)||{},l=s.delay||r.delay||0,{elapsed:u=0}=r;u-=nl(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-u,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{a(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&Object.assign(c,rx(e,c)),c.duration&&(c.duration=nl(c.duration)),c.repeatDelay&&(c.repeatDelay=nl(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(no(c),0!==c.delay||(d=!0)),(ea.instantAnimations||ea.skipAnimations)&&(d=!0,no(c),c.delay=0),c.allowFlatten=!s.type&&!s.ease,d&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(rm),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(c.keyframes,s);if(void 0!==e){ec.update(()=>{c.onUpdate(e),c.onComplete()});return}}return s.isSync?new n8(c):new rp(c)};function rw(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:a,...s}=t;r&&(o=r);let l=[],u=i&&e.animationState&&e.animationState.getState()[i];for(let t in s){let r=e.getValue(t,e.latestValues[t]??null),i=s[t];if(void 0===i||u&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(u,t))continue;let a={delay:n,...nn(o||{},t)},c=r.get();if(void 0!==c&&!r.isAnimating&&!Array.isArray(i)&&i===c&&!a.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=e.props[t6];if(n){let e=window.MotionHandoffAnimation(n,t,ec);null!==e&&(a.startTime=e,d=!0)}}ni(e,t),r.start(rP(t,r,i,e.shouldReduceMotion&&V.has(t)?{type:!1}:a,e,d));let h=r.animation;h&&l.push(h)}return a&&Promise.all(l).then(()=>{ec.update(()=>{a&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=nt(e,t)||{};for(let t in i={...i,...n}){var o;let n=nr(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,e7(n))}}(e,a)})}),l}function rT(e,t,n,r=0,i=1){let o=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),a=e.size,s=(a-1)*r;return"function"==typeof n?n(o,a):1===i?o*r:s-o*r}function rR(e,t,n={}){let r=nt(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(rw(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,n=0,r=0,i=0,o=1,a){let s=[];for(let l of e.variantChildren)l.notify("AnimationStart",t),s.push(rR(l,t,{...a,delay:n+("function"==typeof r?0:r)+rT(e.variantChildren,l,r,i,o)}).then(()=>l.notify("AnimationComplete",t)));return Promise.all(s)}(e,t,r,o,a,s,n)}:()=>Promise.resolve(),{when:s}=i;if(!s)return Promise.all([o(),a(n.delay)]);{let[e,t]="beforeChildren"===s?[o,a]:[a,o];return e().then(()=>t())}}function rS(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}let rE=tm.length,rM=[...tp].reverse(),r_=tp.length;function rj(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rA(){return{animate:rj(!0),whileInView:rj(),whileHover:rj(),whileTap:rj(),whileDrag:rj(),whileFocus:rj(),exit:rj()}}class rC{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rO extends rC{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>rR(e,t,n)));else if("string"==typeof t)r=rR(e,t,n);else{let i="function"==typeof t?nt(e,t,n.custom):t;r=Promise.all(rw(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=rA(),r=!0,i=t=>(n,r)=>{let i=nt(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function o(o){let{props:a}=e,s=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<rE;e++){let r=tm[e],i=t.props[r];(tf(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},l=[],u=new Set,c={},d=1/0;for(let t=0;t<r_;t++){var h,f;let p=rM[t],m=n[p],g=void 0!==a[p]?a[p]:s[p],y=tf(g),v=p===o?m.isActive:null;!1===v&&(d=t);let b=g===s[p]&&g!==a[p]&&y;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),m.protectedKeys={...c},!m.isActive&&null===v||!g&&!m.prevProp||th(g)||"boolean"==typeof g)continue;let x=(h=m.prevProp,"string"==typeof(f=g)?f!==h:!!Array.isArray(f)&&!rS(f,h)),P=x||p===o&&m.isActive&&!b&&y||t>d&&y,w=!1,T=Array.isArray(g)?g:[g],R=T.reduce(i(p),{});!1===v&&(R={});let{prevResolvedValues:S={}}=m,E={...S,...R},M=t=>{P=!0,u.has(t)&&(w=!0,u.delete(t)),m.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in E){let t=R[e],n=S[e];if(c.hasOwnProperty(e))continue;let r=!1;(nr(t)&&nr(n)?rS(t,n):t===n)?void 0!==t&&u.has(e)?M(e):m.protectedKeys[e]=!0:null!=t?M(e):u.add(e)}m.prevProp=g,m.prevResolvedValues=R,m.isActive&&(c={...c,...R}),r&&e.blockInitialAnimation&&(P=!1);let _=b&&x,j=!_||w;P&&j&&l.push(...T.map(t=>{let n={type:p};if("string"==typeof t&&r&&!_&&e.manuallyAnimateOnMount&&e.parent){let{parent:r}=e,i=nt(r,t);if(r.enteringChildren&&i){let{delayChildren:t}=i.transition||{};n.delay=rT(r.enteringChildren,e,t)}}return{animation:t,options:n}}))}if(u.size){let t={};if("boolean"!=typeof a.initial){let n=nt(e,Array.isArray(a.initial)?a.initial[0]:a.initial);n&&n.transition&&(t.transition=n.transition)}u.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),l.push({animation:t})}let p=!!l.length;return r&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(l):Promise.resolve()}return{animateChanges:o,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=o(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=rA(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();th(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rk=0;class rD extends rC{constructor(){super(...arguments),this.id=rk++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rL={x:!1,y:!1};function rV(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let rN=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rU(e){return{point:{x:e.pageX,y:e.pageY}}}let rF=e=>t=>rN(t)&&e(t,rU(t));function rI(e,t,n,r){return rV(e,t,rF(n),r)}function rB(e){return e.max-e.min}function rz(e,t,n,r=.5){e.origin=r,e.originPoint=R(t.min,t.max,e.origin),e.scale=rB(n)/rB(t),e.translate=R(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rH(e,t,n,r){rz(e.x,t.x,n.x,r?r.originX:void 0),rz(e.y,t.y,n.y,r?r.originY:void 0)}function rW(e,t,n){e.min=n.min+t.min,e.max=e.min+rB(t)}function rK(e,t,n){e.min=t.min-n.min,e.max=e.min+rB(t)}function r$(e,t,n){rK(e.x,t.x,n.x),rK(e.y,t.y,n.y)}function rG(e){return[e("x"),e("y")]}let rY=({current:e})=>e?e.ownerDocument.defaultView:null,rX=(e,t)=>Math.abs(e-t);class rq{constructor(e,t,{transformPagePoint:n,contextWindow:r=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rQ(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(rX(e.x,t.x)**2+rX(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=eh;this.history.push({...r,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rZ(t,this.transformPagePoint),ec.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rQ("pointercancel"===e.type?this.lastMoveEventInfo:rZ(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!rN(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.distanceThreshold=o,this.contextWindow=r||window;let a=rZ(rU(e),this.transformPagePoint),{point:s}=a,{timestamp:l}=eh;this.history=[{...s,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rQ(a,this.history)),this.removeListeners=ns(rI(this.contextWindow,"pointermove",this.handlePointerMove),rI(this.contextWindow,"pointerup",this.handlePointerUp),rI(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),ed(this.updatePoint)}}function rZ(e,t){return t?{point:t(e.point)}:e}function rJ(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rQ({point:e},t){return{point:e,delta:rJ(e,r0(t)),offset:rJ(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=r0(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>nl(.1)));)n--;if(!r)return{x:0,y:0};let o=nu(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function r0(e){return e[e.length-1]}function r1(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function r2(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function r5(e,t,n){return{min:r3(e,t),max:r3(e,n)}}function r3(e,t){return"number"==typeof e?e:e[t]||0}let r4=new WeakMap;class r9{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ts(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:n}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rq(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rU(e).point)},onStart:(e,t)=>{var n;let{drag:r,dragPropagation:i,onDragStart:o}=this.getProps();if(r&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(n=r)||"y"===n?rL[n]?null:(rL[n]=!0,()=>{rL[n]=!1}):rL.x||rL.y?null:(rL.x=rL.y=!0,()=>{rL.x=rL.y=!1}),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rG(e=>{let t=this.getAxisMotionValue(e).get()||0;if(H.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=rB(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),o&&ec.postRender(()=>o(e,t)),ni(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rG(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:n,contextWindow:rY(this.visualElement)})}stop(e,t){let n=e||this.latestPointerEvent,r=t||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!r||!n)return;let{velocity:o}=r;this.startAnimation(o);let{onDragEnd:a}=this.getProps();a&&ec.postRender(()=>a(n,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!r6(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?R(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?R(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&t9(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:r1(e.x,n,i),y:r1(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:r5(e,"left","right"),y:r5(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rG(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!t9(t))return!1;let r=t.current;J(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=L(e,n),{scroll:i}=t;return i&&(O(r.x,i.offset.x),O(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),a={x:r2((e=i.layout.layoutBox).x,o.x),y:r2(e.y,o.y)};if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=T(e))}return a}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(rG(a=>{if(!r6(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return ni(this.visualElement,e),n.start(rP(e,n,0,t,this.visualElement,!1))}stopAnimation(){rG(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rG(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){rG(t=>{let{drag:n}=this.getProps();if(!r6(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-R(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!t9(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rG(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=rB(e),i=rB(t);return i>r?n=n1(t.min,t.max-r,e.min):r>i&&(n=n1(e.min,e.max-i,t.min)),N(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),rG(t=>{if(!r6(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(R(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;r4.set(this.visualElement,this);let e=rI(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t9(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),ec.read(t);let i=rV(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rG(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function r6(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class r7 extends rC{constructor(e){super(e),this.removeGroupControls=eo,this.removeListeners=eo,this.controls=new r9(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eo}unmount(){this.removeGroupControls(),this.removeListeners()}}let r8=e=>(t,n)=>{e&&ec.postRender(()=>e(t,n))};class ie extends rC{constructor(){super(...arguments),this.removePointerDownListener=eo}onPointerDown(e){this.session=new rq(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rY(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:r8(e),onStart:r8(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&ec.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=rI(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let it={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ir(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ii={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!W.test(e))return e;e=parseFloat(e)}let n=ir(e,t.target.x),r=ir(e,t.target.y);return`${n}% ${r}%`}},io=!1;class ia extends i.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;(function(e){for(let t in e)t_[t]=e[t],b(t)&&(t_[t].isCSSVariable=!0)})(il),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),io&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),it.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,io=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||ec.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),tt.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;io=!0,r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function is(e){let[t,n]=function(e=!0){let t=(0,i.useContext)(t0);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:r,register:o}=t,a=(0,i.useId)();(0,i.useEffect)(()=>{if(e)return o(a)},[e]);let s=(0,i.useCallback)(()=>e&&r&&r(a),[a,r,e]);return!n&&r?[!1,s]:[!0]}(),r=(0,i.useContext)(tH);return(0,tz.jsx)(ia,{...e,layoutGroup:r,switchLayoutGroup:(0,i.useContext)(t7),isPresent:t,safeToRemove:n})}let il={borderRadius:{...ii,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ii,borderTopRightRadius:ii,borderBottomLeftRadius:ii,borderBottomRightRadius:ii,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eB.parse(e);if(r.length>5)return e;let i=eB.createTransformer(e),o=+("number"!=typeof r[0]),a=n.x.scale*t.x,s=n.y.scale*t.y;r[0+o]/=a,r[1+o]/=s;let l=R(a,s,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};function iu(e){return"object"==typeof e&&null!==e}function ic(e){return iu(e)&&"ownerSVGElement"in e}let id=(e,t)=>e.depth-t.depth;class ih{constructor(){this.children=[],this.isDirty=!1}add(e){e2(this.children,e),this.isDirty=!0}remove(e){e5(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(id),this.isDirty=!1,this.children.forEach(e)}}let ip=["TopLeft","TopRight","BottomLeft","BottomRight"],im=ip.length,ig=e=>"string"==typeof e?parseFloat(e):e,iy=e=>"number"==typeof e||W.test(e);function iv(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let ib=iP(0,.5,nX),ix=iP(.5,.95,eo);function iP(e,t,n){return r=>r<e?0:r>t?1:n(n1(e,t,r))}function iw(e,t){e.min=t.min,e.max=t.max}function iT(e,t){iw(e.x,t.x),iw(e.y,t.y)}function iR(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function iS(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function iE(e,t,[n,r,i],o,a){!function(e,t=0,n=1,r=.5,i,o=e,a=e){if(H.test(t)&&(t=parseFloat(t),t=R(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=R(o.min,o.max,r);e===o&&(s-=t),e.min=iS(e.min,t,n,s,i),e.max=iS(e.max,t,n,s,i)}(e,t[n],t[r],t[i],t.scale,o,a)}let iM=["x","scaleX","originX"],i_=["y","scaleY","originY"];function ij(e,t,n,r){iE(e.x,t,iM,n?n.x:void 0,r?r.x:void 0),iE(e.y,t,i_,n?n.y:void 0,r?r.y:void 0)}function iA(e){return 0===e.translate&&1===e.scale}function iC(e){return iA(e.x)&&iA(e.y)}function iO(e,t){return e.min===t.min&&e.max===t.max}function ik(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function iD(e,t){return ik(e.x,t.x)&&ik(e.y,t.y)}function iL(e){return rB(e.x)/rB(e.y)}function iV(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class iN{constructor(){this.members=[]}add(e){e2(this.members,e),e.scheduleRender()}remove(e){if(e5(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iF=["","X","Y","Z"],iI=0;function iB(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function iz({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=iI++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,el.value&&(iU.nodes=iU.calculatedTargetDeltas=iU.calculatedProjections=0),this.nodes.forEach(iK),this.nodes.forEach(iJ),this.nodes.forEach(iQ),this.nodes.forEach(i$),el.addProjectionMetrics&&el.addProjectionMetrics(iU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new ih)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e3),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=ic(t)&&!(ic(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n;let r=0,i=()=>this.root.updateBlockedByResize=!1;ec.read(()=>{r=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=e1.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(ed(r),e(o-t))};return ec.setup(r,!0),()=>ed(r)}(i,250),it.hasAnimatedSinceResize&&(it.hasAnimatedSinceResize=!1,this.nodes.forEach(iZ)))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||i4,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=i.getProps(),l=!this.targetLayout||!iD(this.targetLayout,r),u=!t&&n;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...nn(o,"layout"),onPlay:a,onComplete:s};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||iZ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ed(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(i0),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[t6];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",ec,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iY);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(iX);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(iq),this.nodes.forEach(iH),this.nodes.forEach(iW)):this.nodes.forEach(iX),this.clearAllSnapshots();let e=e1.now();eh.delta=N(0,1e3/60,e-eh.timestamp),eh.timestamp=e,eh.isProcessing=!0,ef.update.process(eh),ef.preRender.process(eh),ef.render.process(eh),eh.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tt.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iG),this.sharedNodes.forEach(i1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ec.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ec.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||rB(this.snapshot.measuredBox.x)||rB(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ts(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!iC(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||M(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),i7((t=r).x),i7(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return ts();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(oe))){let{scroll:e}=this.root;e&&(O(t.x,e.offset.x),O(t.y,e.offset.y))}return t}removeElementScroll(e){let t=ts();if(iT(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&iT(t,e),O(t.x,i.offset.x),O(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=ts();iT(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&D(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),M(r.latestValues)&&D(n,r.latestValues)}return M(this.latestValues)&&D(n,this.latestValues),n}removeTransform(e){let t=ts();iT(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!M(n.latestValues))continue;E(n.latestValues)&&n.updateSnapshot();let r=ts();iT(r,n.measurePageBox()),ij(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return M(this.latestValues)&&ij(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eh.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=eh.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ts(),this.relativeTargetOrigin=ts(),r$(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),iT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ts(),this.targetWithTransforms=ts()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,a,s;this.forceRelativeParentToResolveTarget(),o=this.target,a=this.relativeTarget,s=this.relativeParent.target,rW(o.x,a.x,s.x),rW(o.y,a.y,s.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iT(this.target,this.layout.layoutBox),C(this.target,this.targetDelta)):iT(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ts(),this.relativeTargetOrigin=ts(),r$(this.relativeTargetOrigin,this.target,e.target),iT(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}el.value&&iU.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||E(this.parent.latestValues)||_(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===eh.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;iT(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(e,t,n,r=!1){let i,o;let a=n.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=n[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&D(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,C(e,o)),r&&M(i.latestValues)&&D(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=ts());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iR(this.prevProjectionDelta.x,this.projectionDelta.x),iR(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rH(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&iV(this.projectionDelta.x,this.prevProjectionDelta.x)&&iV(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s)),el.value&&iU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=to(),this.projectionDelta=to(),this.projectionDeltaWithTransform=to()}setAnimationOrigin(e,t=!1){let n;let r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},a=to();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=ts(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(i3));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(i2(a.x,e.x,r),i2(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,g;if(r$(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=s,g=r,i5(f.x,p.x,m.x,g),i5(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,h=n,iO(u.x,h.x)&&iO(u.y,h.y)))this.isProjectionDirty=!1;n||(n=ts()),iT(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=R(0,n.opacity??1,ib(r)),e.opacityExit=R(t.opacity??1,0,ix(r))):o&&(e.opacity=R(t.opacity??1,n.opacity??1,r));for(let i=0;i<im;i++){let o=`border${ip[i]}Radius`,a=iv(t,o),s=iv(n,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||iy(a)===iy(s)?(e[o]=Math.max(R(ig(a),ig(s),r),0),(H.test(s)||H.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||n.rotate)&&(e.rotate=R(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ed(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ec.update(()=>{it.hasAnimatedSinceResize=!0,nc.layout++,this.motionValue||(this.motionValue=e7(0)),this.currentAnimation=function(e,t,n){let r=eQ(e)?e:e7(e);return r.start(rP("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{nc.layout--},onComplete:()=>{nc.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&i8(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||ts();let t=rB(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=rB(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}iT(t,n),D(t,i),rH(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new iN),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&iB("z",e,r,this.animationValues);for(let t=0;t<iF.length;t++)iB(`rotate${iF[t]}`,e,r,this.animationValues),iB(`skew${iF[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let n=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=t1(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=t1(t?.pointerEvents)||""),this.hasProjected&&!M(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1);return}e.visibility="";let i=r.animationValues||r.latestValues;this.applyTransformsToTarget();let o=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=n?.z||0;if((i||o||a)&&(r=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),a&&(r+=`skewX(${a}deg) `),s&&(r+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(r+=`scale(${s}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);n&&(o=n(i,o)),e.transform=o;let{x:a,y:s}=this.projectionDelta;for(let t in e.transformOrigin=`${100*a.origin}% ${100*s.origin}% 0`,r.animationValues?e.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:e.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,t_){if(void 0===i[t])continue;let{correct:n,applyTo:a,isCSSVariable:s}=t_[t],l="none"===o?i[t]:n(i[t],r);if(a){let t=a.length;for(let n=0;n<t;n++)e[a[n]]=l}else s?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?t1(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(iY),this.root.sharedNodes.clear()}}}function iH(e){e.updateLayout()}function iW(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?rG(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=rB(r);r.min=n[e].min,r.max=r.min+i}):i8(i,t.layoutBox,n)&&rG(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],a=rB(n[r]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=to();rH(a,n,t.layoutBox);let s=to();o?rH(s,e.applyTransform(r,!0),t.measuredBox):rH(s,n,t.layoutBox);let l=!iC(a),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let a=ts();r$(a,t.layoutBox,i.layoutBox);let s=ts();r$(s,n,o.layoutBox),iD(a,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function iK(e){el.value&&iU.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function i$(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function iG(e){e.clearSnapshot()}function iY(e){e.clearMeasurements()}function iX(e){e.isLayoutDirty=!1}function iq(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function iZ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function iJ(e){e.resolveTargetDelta()}function iQ(e){e.calcProjection()}function i0(e){e.resetSkewAndRotation()}function i1(e){e.removeLeadSnapshot()}function i2(e,t,n){e.translate=R(t.translate,0,n),e.scale=R(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function i5(e,t,n,r){e.min=R(t.min,n.min,r),e.max=R(t.max,n.max,r)}function i3(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let i4={duration:.45,ease:[.4,0,.1,1]},i9=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),i6=i9("applewebkit/")&&!i9("chrome/")?Math.round:eo;function i7(e){e.min=i6(e.min),e.max=i6(e.max)}function i8(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(iL(t)-iL(n)))}function oe(e){return e!==e.root&&e.scroll?.wasRoot}let ot=iz({attachResizeListener:(e,t)=>rV(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),on={current:void 0},or=iz({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!on.current){let e=new ot({});e.mount(window),e.setOptions({layoutScroll:!0}),on.current=e}return on.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function oi(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function oo(e){return!("touch"===e.pointerType||rL.x||rL.y)}function oa(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&ec.postRender(()=>i(t,rU(t)))}class os extends rC{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=oi(e,n),a=e=>{if(!oo(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{oo(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,(e,t)=>(oa(this.node,t,"Start"),e=>oa(this.node,e,"End"))))}unmount(){}}class ol extends rC{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ns(rV(this.node.current,"focus",()=>this.onFocus()),rV(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let ou=(e,t)=>!!t&&(e===t||ou(e,t.parentElement)),oc=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),od=new WeakSet;function oh(e){return t=>{"Enter"===t.key&&e(t)}}function of(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let op=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=oh(()=>{if(od.has(n))return;of(n,"down");let e=oh(()=>{of(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>of(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function om(e){return rN(e)&&!(rL.x||rL.y)}function og(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&ec.postRender(()=>i(t,rU(t)))}class oy extends rC{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=oi(e,n),a=e=>{let r=e.currentTarget;if(!om(e))return;od.add(r);let o=t(r,e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),od.has(r)&&od.delete(r),om(e)&&"function"==typeof o&&o(e,{success:t})},s=e=>{a(e,r===window||r===document||n.useGlobalTarget||ou(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{(n.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),iu(e)&&"offsetHeight"in e&&(e.addEventListener("focus",e=>op(e,i)),!oc.has(e.tagName)&&-1===e.tabIndex&&!e.hasAttribute("tabindex")&&(e.tabIndex=0))}),o}(e,(e,t)=>(og(this.node,t,"Start"),(e,{success:t})=>og(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ov=new WeakMap,ob=new WeakMap,ox=e=>{let t=ov.get(e.target);t&&t(e)},oP=e=>{e.forEach(ox)},ow={some:0,all:1};class oT extends rC{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:ow[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ob.has(n)||ob.set(n,{});let r=ob.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(oP,{root:e,...t})),r[i]}(t);return ov.set(e,n),r.observe(e),()=>{ov.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let oR=function(e,t){if("undefined"==typeof Proxy)return ne;let n=new Map,r=(n,r)=>ne(n,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(i,o)=>"create"===o?r:(n.has(o)||n.set(o,ne(o,void 0,e,t)),n.get(o))})}({animation:{Feature:rO},exit:{Feature:rD},inView:{Feature:oT},tap:{Feature:oy},focus:{Feature:ol},hover:{Feature:os},pan:{Feature:ie},drag:{Feature:r7,ProjectionNode:or,MeasureLayout:is},layout:{ProjectionNode:or,MeasureLayout:is}},(e,t)=>tB(e)?new tF(t):new tC(t,{allowProjection:e!==i.Fragment}))},1329:(e,t,n)=>{n.d(t,{DX:()=>s,TL:()=>a});var r=n(3210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var o=n(687);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var a;let e,s;let l=(a=n,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...a}=e,s=r.Children.toArray(i),l=s.find(u);if(l){let e=l.props.children,i=s.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...a,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var s=a("Slot"),l=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},1500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,o,a,s,l){if(0===Object.keys(o[1]).length){t.head=s;return}for(let u in o[1]){let c;let d=o[1][u],h=d[0],f=(0,r.createRouterCacheKey)(h),p=null!==a&&void 0!==a[2][u]?a[2][u]:null;if(n){let r=n.parallelRoutes.get(u);if(r){let n;let o=(null==l?void 0:l.kind)==="auto"&&l.status===i.PrefetchCacheEntryStatus.reusable,a=new Map(r),c=a.get(f);n=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes)}:o&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),loading:null},a.set(f,n),e(n,c,d,p||null,s,l),t.parallelRoutes.set(u,a);continue}}if(null!==p){let e=p[1],t=p[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let m=t.parallelRoutes.get(u);m?m.set(f,c):t.parallelRoutes.set(u,new Map([[f,c]])),e(c,void 0,d,p,s,l)}}}});let r=n(3123),i=n(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1520:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useSyncDevRenderIndicator",{enumerable:!0,get:function(){return r}});let n=e=>e(),r=()=>n;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1550:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},1992:(e,t)=>{function n(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return n}})},2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],i=n[0];if(Array.isArray(r)&&Array.isArray(i)){if(r[0]!==i[0]||r[2]!==i[2])return!0}else if(r!==i)return!0;if(t[4])return!n[4];if(n[4])return!0;let o=Object.values(t[1])[0],a=Object.values(n[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2157:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let r=n(1550);function i(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},2308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,i,,a]=t;for(let s in r.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=n,t[3]="refresh"),i)e(i[s],n)}},refreshInactiveParallelSegments:function(){return a}});let r=n(6928),i=n(9008),o=n(3913);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{state:t,updatedTree:n,updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u=n,canonicalUrl:c}=e,[,d,h,f]=n,p=[];if(h&&h!==c&&"refresh"===f&&!l.has(h)){l.add(h);let e=(0,i.fetchServerResponse)(new URL(h,location.origin),{flightRouterState:[u[0],u[1],u[2],"refetch"],nextUrl:a?t.nextUrl:null}).then(e=>{let{flightData:t}=e;if("string"!=typeof t)for(let e of t)(0,r.applyFlightData)(o,o,e)});p.push(e)}for(let e in d){let n=s({state:t,updatedTree:d[e],updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u,canonicalUrl:c});p.push(n)}await Promise.all(p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2348:(e,t,n)=>{n.d(t,{QP:()=>X});let r=e=>{let t=s(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),i(n,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let i=n[e]||[];return t&&r[e]?[...i,...r[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),o=r?i(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},o=/^\[(.+)\]$/,a=e=>{if(o.test(e)){let t=o.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},s=e=>{let{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),n).forEach(([e,n])=>{l(n,r,e,t)}),r},l=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=n;return}if("function"==typeof e){if(c(e)){l(e(r),t,n,r);return}t.validators.push({validator:e,classGroupId:n});return}Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),n,r)})})},u=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,n])=>[e,n.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,n])=>[t+e,n])):e)]):e,h=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}},f=e=>{let{separator:t,experimentalParseClassName:n}=e,r=1===t.length,i=t[0],o=t.length,a=e=>{let n;let a=[],s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===i&&(r||e.slice(u,u+o)===t)){a.push(e.slice(l,u)),l=u+o;continue}if("/"===c){n=u;continue}}"["===c?s++:"]"===c&&s--}let u=0===a.length?e:e.substring(l),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:a,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:n&&n>l?n-l:void 0}};return n?e=>n({className:e,parseClassName:a}):a},p=e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{"["===e[0]?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t},m=e=>({cache:h(e.cacheSize),parseClassName:f(e),...r(e)}),g=/\s+/,y=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i}=t,o=[],a=e.trim().split(g),s="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:l,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=n(t),h=!!d,f=r(h?c.substring(0,d):c);if(!f){if(!h||!(f=r(c))){s=t+(s.length>0?" "+s:s);continue}h=!1}let m=p(l).join(":"),g=u?m+"!":m,y=g+f;if(o.includes(y))continue;o.push(y);let v=i(f,h);for(let e=0;e<v.length;++e){let t=v[e];o.push(g+t)}s=t+(s.length>0?" "+s:s)}return s};function v(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=b(e))&&(r&&(r+=" "),r+=t);return r}let b=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=b(e[r]))&&(n&&(n+=" "),n+=t);return n},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},P=/^\[(?:([a-z-]+):)?(.+)\]$/i,w=/^\d+\/\d+$/,T=new Set(["px","full","screen"]),R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,_=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,j=e=>C(e)||T.has(e)||w.test(e),A=e=>W(e,"length",K),C=e=>!!e&&!Number.isNaN(Number(e)),O=e=>W(e,"number",C),k=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&C(e.slice(0,-1)),L=e=>P.test(e),V=e=>R.test(e),N=new Set(["length","size","percentage"]),U=e=>W(e,N,$),F=e=>W(e,"position",$),I=new Set(["image","url"]),B=e=>W(e,I,Y),z=e=>W(e,"",G),H=()=>!0,W=(e,t,n)=>{let r=P.exec(e);return!!r&&(r[1]?"string"==typeof t?r[1]===t:t.has(r[1]):n(r[2]))},K=e=>S.test(e)&&!E.test(e),$=()=>!1,G=e=>M.test(e),Y=e=>_.test(e);Symbol.toStringTag;let X=function(e,...t){let n,r,i;let o=function(s){return r=(n=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=n.cache.set,o=a,a(s)};function a(e){let t=r(e);if(t)return t;let o=y(e,n);return i(e,o),o}return function(){return o(v.apply(null,arguments))}}(()=>{let e=x("colors"),t=x("spacing"),n=x("blur"),r=x("brightness"),i=x("borderColor"),o=x("borderRadius"),a=x("borderSpacing"),s=x("borderWidth"),l=x("contrast"),u=x("grayscale"),c=x("hueRotate"),d=x("invert"),h=x("gap"),f=x("gradientColorStops"),p=x("gradientColorStopPositions"),m=x("inset"),g=x("margin"),y=x("opacity"),v=x("padding"),b=x("saturate"),P=x("scale"),w=x("sepia"),T=x("skew"),R=x("space"),S=x("translate"),E=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto",L,t],N=()=>[L,t],I=()=>["",j,A],W=()=>["auto",C,L],K=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Y=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",L],q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[C,L];return{cacheSize:500,separator:":",theme:{colors:[H],spacing:[j,A],blur:["none","",V,L],brightness:Z(),borderColor:[e],borderRadius:["none","","full",V,L],borderSpacing:N(),borderWidth:I(),contrast:Z(),grayscale:X(),hueRotate:Z(),invert:X(),gap:N(),gradientColorStops:[e],gradientColorStopPositions:[D,A],inset:_(),margin:_(),opacity:Z(),padding:N(),saturate:Z(),scale:Z(),sepia:X(),skew:Z(),space:N(),translate:N()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[V]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...K(),L]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",k,L]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",k,L]}],"grid-cols":[{"grid-cols":[H]}],"col-start-end":[{col:["auto",{span:["full",k,L]},L]}],"col-start":[{"col-start":W()}],"col-end":[{"col-end":W()}],"grid-rows":[{"grid-rows":[H]}],"row-start-end":[{row:["auto",{span:[k,L]},L]}],"row-start":[{"row-start":W()}],"row-end":[{"row-end":W()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...Y()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...Y(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...Y(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[R]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[R]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[V]},V]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",V,A]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",O]}],"font-family":[{font:[H]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",C,O]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",j,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",j,A]}],"underline-offset":[{"underline-offset":["auto",j,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...K(),F]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",U]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},B]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:$()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[j,L]}],"outline-w":[{outline:[j,A]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[j,A]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",V,z]}],"shadow-color":[{shadow:[H]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...G(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":G()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",V,L]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[P]}],"scale-x":[{"scale-x":[P]}],"scale-y":[{"scale-y":[P]}],rotate:[{rotate:[k,L]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[j,A,O]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},2575:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},2688:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(3210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:s="",children:l,iconNode:u,...c},d)=>(0,r.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:o("lucide",s),...c},[...u.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},l)=>(0,r.createElement)(s,{ref:l,iconNode:t,className:o(`lucide-${i(e)}`,n),...a}));return n.displayName=`${e}`,n}},2941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},3038:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(3210);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=o(e,r)),t&&(i.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{mountLinkInstance:function(){return u},onLinkVisibilityChanged:function(){return d},onNavigationIntent:function(){return h},pingVisibleLinks:function(){return p},unmountLinkInstance:function(){return c}}),n(8202);let r=n(9752),i=n(9154),o=n(593),a="function"==typeof WeakMap?new WeakMap:new Map,s=new Set,l="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;d(t.target,e)}},{rootMargin:"200px"}):null;function u(e,t,n,i){let o=null;try{if(o=(0,r.createPrefetchURL)(t),null===o)return}catch(e){("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+t+"' because it cannot be converted to a URL.");return}let s={prefetchHref:o.href,router:n,kind:i,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1};void 0!==a.get(e)&&c(e),a.set(e,s),null!==l&&l.observe(e)}function c(e){let t=a.get(e);if(void 0!==t){a.delete(e),s.delete(t);let n=t.prefetchTask;null!==n&&(0,o.cancelPrefetchTask)(n)}null!==l&&l.unobserve(e)}function d(e,t){let n=a.get(e);void 0!==n&&(n.isVisible=t,t?s.add(n):s.delete(n),f(n))}function h(e){let t=a.get(e);void 0!==t&&void 0!==t&&(t.wasHoveredOrTouched=!0,f(t))}function f(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,o.cancelPrefetchTask)(t);return}}function p(e,t){let n=(0,o.getCurrentCacheVersion)();for(let r of s){let a=r.prefetchTask;if(null!==a&&r.cacheVersion===n&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,o.cancelPrefetchTask)(a);let s=(0,o.createCacheKey)(r.prefetchHref,e),l=r.wasHoveredOrTouched?o.PrefetchPriority.Intent:o.PrefetchPriority.Default;r.prefetchTask=(0,o.schedulePrefetchTask)(s,t,r.kind===i.PrefetchKind.FULL,l),r.cacheVersion=(0,o.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let r=n(4400),i=n(1500),o=n(3123),a=n(3913);function s(e,t,n,s,l){let{segmentPath:u,seedData:c,tree:d,head:h}=n,f=e,p=t;for(let e=0;e<u.length;e+=2){let t=u[e],n=u[e+1],m=e===u.length-2,g=(0,o.createRouterCacheKey)(n),y=p.parallelRoutes.get(t);if(!y)continue;let v=f.parallelRoutes.get(t);v&&v!==y||(v=new Map(y),f.parallelRoutes.set(t,v));let b=y.get(g),x=v.get(g);if(m){if(c&&(!x||!x.lazyData||x===b)){let e=c[0],t=c[1],n=c[3];x={lazyData:null,rsc:l||e!==a.PAGE_SEGMENT_KEY?t:null,prefetchRsc:null,head:null,prefetchHead:null,loading:n,parallelRoutes:l&&b?new Map(b.parallelRoutes):new Map},b&&l&&(0,r.invalidateCacheByRouterState)(x,b,d),l&&(0,i.fillLazyItemsTillLeafWithHead)(x,b,d,c,h,s),v.set(g,x)}continue}x&&b&&(x===b&&(x={lazyData:x.lazyData,rsc:x.rsc,prefetchRsc:x.prefetchRsc,head:x.head,prefetchHead:x.prefetchHead,parallelRoutes:new Map(x.parallelRoutes),loading:x.loading},v.set(g,x)),f=x,p=b)}}function l(e,t,n,r){s(e,t,n,r,!0)}function u(e,t,n,r){s(e,t,n,r,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3931:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},4224:(e,t,n)=>{n.d(t,{F:()=>a});var r=n(9384);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==s?void 0:s[e];if(null===t)return null;let o=i(t)||i(r);return a[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...s,...u}[t]):({...s,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},4397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let r=n(3123);function i(e,t){return function e(t,n,i){if(0===Object.keys(n).length)return[t,i];if(n.children){let[o,a]=n.children,s=t.parallelRoutes.get("children");if(s){let t=(0,r.createRouterCacheKey)(o),n=s.get(t);if(n){let r=e(n,a,i+"/"+t);if(r)return r}}}for(let o in n){if("children"===o)continue;let[a,s]=n[o],l=t.parallelRoutes.get(o);if(!l)continue;let u=(0,r.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let r=n(3123);function i(e,t,n){for(let i in n[1]){let o=n[1][i][0],a=(0,r.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4545:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{useReducer:function(){return s},useUnwrapState:function(){return a}});let r=n(740)._(n(3210)),i=n(1992),o=n(1520);function a(e){return(0,i.isThenable)(e)?(0,r.use)(e):e}function s(e){let[t,n]=r.default.useState(e.state),i=(0,o.useSyncDevRenderIndicator)();return[t,(0,r.useCallback)(t=>{i(()=>{e.dispatch(t,n)})},[e,i])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},4674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let r=n(4949),i=n(1550),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:o}=(0,i.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},5076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return o},prefetchReducer:function(){return a}});let r=n(5144),i=n(5334),o=new r.PromiseQueue(5),a=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let r=n(6312),i=n(9656);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,n;let i=new Promise((e,r)=>{t=e,n=r}),o=async()=>{try{r._(this,a)[a]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,a)[a]--,r._(this,l)[l]()}};return r._(this,s)[s].push({promiseFn:i,task:o}),r._(this,l)[l](),i}bump(e){let t=r._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,s)[s].splice(t,1)[0];r._(this,s)[s].unshift(e),r._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),r._(this,o)[o]=e,r._(this,a)[a]=0,r._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(r._(this,a)[a]<r._(this,o)[o]||e)&&r._(this,s)[s].length>0){var t;null==(t=r._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:P,isExternalUrl:w,navigateType:T,shouldScroll:R,allowAliasing:S}=n,E={},{hash:M}=P,_=(0,i.createHrefFromUrl)(P),j="push"===T;if((0,g.prunePrefetchCache)(t.prefetchCache),E.preserveCustomHistoryState=!1,E.pendingPush=j,w)return b(t,E,P.toString(),j);if(document.getElementById("__next-page-redirect"))return b(t,E,_,j);let A=(0,g.getOrCreatePrefetchCacheEntry)({url:P,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:S}),{treeAtTimeOfPrefetch:C,data:O}=A;return h.prefetchQueue.bump(O),O.then(h=>{let{flightData:g,canonicalUrl:w,postponed:T}=h,S=!1;if(A.lastUsedTime||(A.lastUsedTime=Date.now(),S=!0),A.aliased){let r=(0,v.handleAliasedPrefetchEntry)(t,g,P,E);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return b(t,E,g,j);let O=w?(0,i.createHrefFromUrl)(w):_;if(M&&t.canonicalUrl.split("#",1)[0]===O.split("#",1)[0])return E.onlyHashChange=!0,E.canonicalUrl=O,E.shouldScroll=R,E.hashFragment=M,E.scrollableSegments=[],(0,c.handleMutable)(t,E);let k=t.tree,D=t.cache,L=[];for(let e of g){let{pathToSegment:n,seedData:i,head:c,isHeadPartial:h,isRootRender:g}=e,v=e.tree,w=["",...n],R=(0,a.applyRouterStatePatchToTree)(w,k,v,_);if(null===R&&(R=(0,a.applyRouterStatePatchToTree)(w,C,v,_)),null!==R){if(i&&g&&T){let e=(0,m.startPPRNavigation)(D,k,v,i,c,h,!1,L);if(null!==e){if(null===e.route)return b(t,E,_,j);R=e.route;let n=e.node;null!==n&&(E.cache=n);let i=e.dynamicRequestTree;if(null!==i){let n=(0,r.fetchServerResponse)(P,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,n)}}else R=v}else{if((0,l.isNavigatingToNewRootLayout)(k,R))return b(t,E,_,j);let r=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(A.status!==u.PrefetchCacheEntryStatus.stale||S?i=(0,d.applyFlightData)(D,r,e,A):(i=function(e,t,n,r){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),x(r).map(e=>[...n,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(r,D,n,v),A.lastUsedTime=Date.now()),(0,s.shouldHardNavigate)(w,k)?(r.rsc=D.rsc,r.prefetchRsc=D.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(r,D,n),E.cache=r):i&&(E.cache=r,D=r),x(v))){let e=[...n,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&L.push(e)}}k=R}}return E.patchedTree=k,E.canonicalUrl=O,E.scrollableSegments=L,E.hashFragment=M,E.shouldScroll=R,(0,c.handleMutable)(t,E)},()=>t)}}});let r=n(9008),i=n(7391),o=n(8468),a=n(6770),s=n(5951),l=n(2030),u=n(9154),c=n(9435),d=n(6928),h=n(5076),f=n(9752),p=n(3913),m=n(5956),g=n(5334),y=n(7464),v=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function x(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,i]of Object.entries(r))for(let r of x(i))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let r=n(9008),i=n(9154),o=n(5076);function a(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function s(e,t,n){return a(e,t===i.PrefetchKind.FULL,n)}function l(e){let{url:t,nextUrl:n,tree:r,prefetchCache:o,kind:s,allowAliasing:l=!0}=e,u=function(e,t,n,r,o){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[n,null])){let n=a(e,!0,s),l=a(e,!1,s),u=e.search?n:l,c=r.get(u);if(c&&o){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=r.get(l);if(o&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&o){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,n,o,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:r,url:t,nextUrl:n,prefetchCache:o,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:r,url:t,nextUrl:n,prefetchCache:o,kind:s||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:n,prefetchCache:r,url:o,data:a,kind:l}=e,u=a.couldBeIntercepted?s(o,l,t):s(o,l),c={treeAtTimeOfPrefetch:n,data:Promise.resolve(a),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:o};return r.set(u,c),c}function c(e){let{url:t,kind:n,tree:a,nextUrl:l,prefetchCache:u}=e,c=s(t,n),d=o.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:a,nextUrl:l,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:i}=e,o=r.get(i);if(!o)return;let a=s(t,o.kind,n);return r.set(a,{...o,key:a}),r.delete(i),a}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=n?n:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),h={treeAtTimeOfPrefetch:a,data:d,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,h),h}function d(e){for(let[t,n]of e)p(n)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let h=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:o}=e;return -1!==o?Date.now()<n+o?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+h?r?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<n+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<n+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return l},isBot:function(){return s}});let r=n(5796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=r.HTML_LIMITED_BOT_UA_RE.source;function a(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||a(e)}function l(e){return i.test(e)?"dom":a(e)?"html":void 0}},5796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview/i},5814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return p}});let r=n(4985),i=n(687),o=r._(n(3210)),a=n(195),s=n(2142),l=n(9154),u=n(3038),c=n(9289),d=n(6127);n(148);let h=n(3406);function f(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}let p=o.default.forwardRef(function(e,t){let n,r;let{href:a,as:p,children:m,prefetch:g=null,passHref:y,replace:v,shallow:b,scroll:x,onClick:P,onMouseEnter:w,onTouchStart:T,legacyBehavior:R=!1,...S}=e;n=m,R&&("string"==typeof n||"number"==typeof n)&&(n=(0,i.jsx)("a",{children:n}));let E=o.default.useContext(s.AppRouterContext),M=!1!==g,_=null===g?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:j,as:A}=o.default.useMemo(()=>{let e=f(a);return{href:e,as:p?f(p):e}},[a,p]);R&&(r=o.default.Children.only(n));let C=R?r&&"object"==typeof r&&r.ref:t,O=o.default.useCallback(e=>(M&&null!==E&&(0,h.mountLinkInstance)(e,j,E,_),()=>{(0,h.unmountLinkInstance)(e)}),[M,j,E,_]),k={ref:(0,u.useMergedRef)(O,C),onClick(e){R||"function"!=typeof P||P(e),R&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),E&&!e.defaultPrevented&&!function(e,t,n,r,i,a,s){let{nodeName:l}=e.currentTarget;!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e))&&(e.preventDefault(),o.default.startTransition(()=>{let e=null==s||s;"beforePopState"in t?t[i?"replace":"push"](n,r,{shallow:a,scroll:e}):t[i?"replace":"push"](r||n,{scroll:e})}))}(e,E,j,A,v,b,x)},onMouseEnter(e){R||"function"!=typeof w||w(e),R&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),E&&M&&(0,h.onNavigationIntent)(e.currentTarget)},onTouchStart:function(e){R||"function"!=typeof T||T(e),R&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),E&&M&&(0,h.onNavigationIntent)(e.currentTarget)}};return(0,c.isAbsoluteUrl)(A)?k.href=A:R&&!y&&("a"!==r.type||"href"in r.props)||(k.href=(0,d.addBasePath)(A)),R?o.default.cloneElement(r,k):(0,i.jsx)("a",{...S,...k,children:n})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[o,a]=n,[s,l]=t;return(0,i.matchSegment)(s,o)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),a[l]):!!Array.isArray(s)}}});let r=n(4007),i=n(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return f},listenForDynamicRequest:function(){return h},startPPRNavigation:function(){return l},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],i=t.parallelRoutes,a=new Map(i);for(let t in r){let n=r[t],s=n[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let r=u.get(l);if(void 0!==r){let i=e(r,n),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=g(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a}}}});let r=n(3913),i=n(4077),o=n(3123),a=n(2030),s={route:null,node:null,dynamicRequestTree:null,children:null};function l(e,t,n,a,l,d,h,f){return function e(t,n,a,l,d,h,f,p,m,g){let y=n[1],v=a[1],b=null!==d?d[2]:null;l||!0!==a[4]||(l=!0);let x=t.parallelRoutes,P=new Map(x),w={},T=null,R=!1,S={};for(let t in v){let n;let a=v[t],c=y[t],d=x.get(t),E=null!==b?b[t]:null,M=a[0],_=m.concat([t,M]),j=(0,o.createRouterCacheKey)(M),A=void 0!==c?c[0]:void 0,C=void 0!==d?d.get(j):void 0;if(null!==(n=M===r.DEFAULT_SEGMENT_KEY?void 0!==c?{route:c,node:null,dynamicRequestTree:null,children:null}:u(c,a,l,void 0!==E?E:null,h,f,_,g):p&&0===Object.keys(a[1]).length?u(c,a,l,void 0!==E?E:null,h,f,_,g):void 0!==c&&void 0!==A&&(0,i.matchSegment)(M,A)&&void 0!==C&&void 0!==c?e(C,c,a,l,E,h,f,p,_,g):u(c,a,l,void 0!==E?E:null,h,f,_,g))){if(null===n.route)return s;null===T&&(T=new Map),T.set(t,n);let e=n.node;if(null!==e){let n=new Map(d);n.set(j,e),P.set(t,n)}let r=n.route;w[t]=r;let i=n.dynamicRequestTree;null!==i?(R=!0,S[t]=i):S[t]=r}else w[t]=a,S[t]=a}if(null===T)return null;let E={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:P};return{route:c(a,w),node:E,dynamicRequestTree:R?c(a,S):null,children:T}}(e,t,n,!1,a,l,d,h,[],f)}function u(e,t,n,r,i,l,u,h){return!n&&(void 0===e||(0,a.isNavigatingToNewRootLayout)(e,t))?s:function e(t,n,r,i,a,s){if(null===n)return d(t,null,r,i,a,s);let l=t[1],u=n[4],h=0===Object.keys(l).length;if(u||i&&h)return d(t,n,r,i,a,s);let f=n[2],p=new Map,m=new Map,g={},y=!1;if(h)s.push(a);else for(let t in l){let n=l[t],u=null!==f?f[t]:null,c=n[0],d=a.concat([t,c]),h=(0,o.createRouterCacheKey)(c),v=e(n,u,r,i,d,s);p.set(t,v);let b=v.dynamicRequestTree;null!==b?(y=!0,g[t]=b):g[t]=n;let x=v.node;if(null!==x){let e=new Map;e.set(h,x),m.set(t,e)}}return{route:t,node:{lazyData:null,rsc:n[1],prefetchRsc:null,head:h?r:null,prefetchHead:null,loading:n[3],parallelRoutes:m},dynamicRequestTree:y?c(t,g):null,children:p}}(t,r,i,l,u,h)}function c(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function d(e,t,n,r,i,a){let s=c(e,e[1]);return s[3]="refetch",{route:e,node:function e(t,n,r,i,a,s){let l=t[1],u=null!==n?n[2]:null,c=new Map;for(let t in l){let n=l[t],d=null!==u?u[t]:null,h=n[0],f=a.concat([t,h]),p=(0,o.createRouterCacheKey)(h),m=e(n,void 0===d?null:d,r,i,f,s),g=new Map;g.set(p,m),c.set(t,g)}let d=0===c.size;d&&s.push(a);let h=null!==n?n[1]:null,f=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:c,prefetchRsc:void 0!==h?h:null,prefetchHead:d?r:[null,null],loading:void 0!==f?f:null,rsc:y(),head:d?y():null}}(e,t,n,r,i,a),dynamicRequestTree:s,children:null}}function h(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:a,head:s}=t;a&&function(e,t,n,r,a){let s=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],o=s.children;if(null!==o){let e=o.get(n);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(r,t)){s=e;continue}}}return}(function e(t,n,r,a){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,n,r,a,s){let l=n[1],u=r[1],c=a[2],d=t.parallelRoutes;for(let t in l){let n=l[t],r=u[t],a=c[t],h=d.get(t),f=n[0],m=(0,o.createRouterCacheKey)(f),g=void 0!==h?h.get(m):void 0;void 0!==g&&(void 0!==r&&(0,i.matchSegment)(f,r[0])&&null!=a?e(g,n,r,a,s):p(n,g,null))}let h=t.rsc,f=a[1];null===h?t.rsc=f:g(h)&&h.resolve(f);let m=t.head;g(m)&&m.resolve(s)}(l,t.route,n,r,a),t.dynamicRequestTree=null);return}let u=n[1],c=r[2];for(let t in n){let n=u[t],r=c[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(n[0],t)&&null!=r)return e(o,n,r,a)}}})(s,n,r,a)}(e,n,r,a,s)}f(e,null)}},t=>{f(e,t)})}function f(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)p(e.route,n,t);else for(let e of r.values())f(e,t);e.dynamicRequestTree=null}function p(e,t,n){let r=e[1],i=t.parallelRoutes;for(let e in r){let t=r[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&p(t,u,n)}let a=t.rsc;g(a)&&(null===n?a.resolve(null):a.reject(n));let s=t.head;g(s)&&s.resolve(null)}let m=Symbol();function g(e){return e&&e.tag===m}function y(){let e,t;let n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=m,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6085:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},6127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let r=n(8834),i=n(4674);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6312:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let r=n(6127);function i(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let r=n(5232);function i(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},6736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let r=n(2255);function i(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,l){let u;let[c,d,h,f,p]=n;if(1===t.length){let e=s(n,r);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,o.matchSegment)(m,c))return null;if(2===t.length)u=s(d[g],r);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[g],r,l)))return null;let y=[t[0],{...d,[g]:u},h,f];return p&&(y[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let r=n(3913),i=n(4007),o=n(4077),a=n(2308);function s(e,t){let[n,i]=e,[a,l]=t;if(a===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(n,a)){let t={};for(let e in i)void 0!==l[e]?t[e]=s(i[e],l[e]):t[e]=i[e];for(let e in l)!t[e]&&(t[e]=l[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let r=n(1500),i=n(3898);function o(e,t,n,o){let{tree:a,seedData:s,head:l,isRootRender:u}=n;if(null===s)return!1;if(u){let n=s[1];t.loading=s[3],t.rsc=n,t.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(t,e,a,s,l,o)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,i.fillCacheWithNewSubTreeData)(t,e,n,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let r=n(3210),i=n(1215),o="next-route-announcer";function a(e){let{tree:t}=e,[n,a]=(0,r.useState)(null);(0,r.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,r.useState)(""),u=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),n?(0,i.createPortal)(s,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,o){let a=o.length<=2,[s,l]=o,u=(0,i.createRouterCacheKey)(l),c=n.parallelRoutes.get(s),d=t.parallelRoutes.get(s);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d));let h=null==c?void 0:c.get(u),f=d.get(u);if(a){f&&f.lazyData&&f!==h||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}if(!f||!h){f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null});return}return f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},d.set(u,f)),e(f,h,(0,r.getNextFlightSegmentPath)(o))}}});let r=n(4007),i=n(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7810:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return j}});let r=n(1264),i=n(1448),o=n(1563),a=n(9154),s=n(6361),l=n(7391),u=n(5232),c=n(6770),d=n(2030),h=n(9435),f=n(1500),p=n(9752),m=n(8214),g=n(6493),y=n(2308),v=n(4007),b=n(6875),x=n(7860),P=n(5334),w=n(5942),T=n(6736),R=n(4642);n(593);let{createFromFetch:S,createTemporaryReferenceSet:E,encodeReply:M}=n(9357);async function _(e,t,n){let a,l,{actionId:u,actionArgs:c}=n,d=E(),h=(0,R.extractInfoFromServerReferenceId)(u),f="use-cache"===h.type?(0,R.omitUnusedArgs)(c,h):c,p=await M(f,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION_HEADER]:u,[o.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[o.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":a=x.RedirectType.push;break;case"replace":a=x.RedirectType.replace;break;default:a=void 0}let P=!!m.headers.get(o.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let w=y?(0,s.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,T=m.headers.get("content-type");if(null==T?void 0:T.startsWith(o.RSC_CONTENT_TYPE_HEADER)){let e=await S(Promise.resolve(m),{callServer:r.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:w,redirectType:a,revalidatedParts:l,isPrerender:P}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:w,redirectType:a,revalidatedParts:l,isPrerender:P}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===T?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:w,redirectType:a,revalidatedParts:l,isPrerender:P}}function j(e,t){let{resolve:n,reject:r}=t,i={},o=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return _(e,s,t).then(async m=>{let v,{actionResult:R,actionFlightData:S,redirectLocation:E,redirectType:M,isPrerender:_,revalidatedParts:j}=m;if(E&&(M===x.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=v=(0,l.createHrefFromUrl)(E,!1)),!S)return(n(R),E)?(0,u.handleExternalUrl)(e,i,E.href,e.pushRef.pendingPush):e;if("string"==typeof S)return n(R),(0,u.handleExternalUrl)(e,i,S,e.pushRef.pendingPush);let A=j.paths.length>0||j.tag||j.cookie;for(let r of S){let{tree:a,seedData:l,head:h,isRootRender:m}=r;if(!m)return console.log("SERVER ACTION APPLY FAILED"),n(R),e;let b=(0,c.applyRouterStatePatchToTree)([""],o,a,v||e.canonicalUrl);if(null===b)return n(R),(0,g.handleSegmentMismatch)(e,t,a);if((0,d.isNavigatingToNewRootLayout)(o,b))return n(R),(0,u.handleExternalUrl)(e,i,v||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],n=(0,p.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(n,void 0,a,l,h,void 0),i.cache=n,i.prefetchCache=new Map,A&&await (0,y.refreshInactiveParallelSegments)({state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,o=b}return E&&v?(A||((0,P.createSeededPrefetchCacheEntry)({url:E,data:{flightData:S,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:_?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,T.hasBasePath)(v)?(0,w.removeBasePath)(v):v,M||x.RedirectType.push))):n(R),(0,h.handleMutable)(e,i)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(9008),n(7391),n(6770),n(2030),n(5232),n(9435),n(6928),n(9752),n(6493),n(8214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8202:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return u},getCurrentAppRouterState:function(){return c}});let r=n(9154),i=n(8830),o=n(3210),a=n(1992);function s(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?l({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function l(e){let{actionQueue:t,action:n,setState:r}=e,i=t.state;t.pending=n;let o=n.payload,l=t.action(i,o);function u(e){!n.discarded&&(t.state=e,s(t,r),n.resolve(e))}(0,a.isThenable)(l)?l.then(u,e=>{s(t,r),n.reject(e)}):u(l)}function u(e){let t={state:e,dispatch:(e,n)=>(function(e,t,n){let i={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,o.startTransition)(()=>{n(e)})}let a={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=a,l({actionQueue:e,action:a,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),l({actionQueue:e,action:a,setState:n})):(null!==e.last&&(e.last.next=a),e.last=a)})(t,e,n),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null};return t}function c(){return null}},8468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,o){let a=o.length<=2,[s,l]=o,u=(0,r.createRouterCacheKey)(l),c=n.parallelRoutes.get(s);if(!c)return;let d=t.parallelRoutes.get(s);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d)),a){d.delete(u);return}let h=c.get(u),f=d.get(u);f&&h&&(f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},d.set(u,f)),e(f,h,(0,i.getNextFlightSegmentPath)(o)))}}});let r=n(3123),i=n(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let r=n(7391),i=n(642);function o(e,t){var n;let{url:o,tree:a}=t,s=(0,r.createHrefFromUrl)(o),l=a||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(n=(0,i.extractPathFromFlightRouterState)(l))?n:o.pathname}}n(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(9154),n(5232),n(9651),n(8627),n(8866),n(5076),n(7936),n(7810);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let r=n(1550);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:i,hash:o}=(0,r.parsePath)(e);return""+t+n+i+o}},8866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let r=n(9008),i=n(7391),o=n(6770),a=n(2030),s=n(5232),l=n(9435),u=n(1500),c=n(9752),d=n(6493),h=n(8214),f=n(2308);function p(e,t){let{origin:n}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,h.hasInterceptionRouteInCurrentTree)(e.tree);return y.lazyData=(0,r.fetchServerResponse)(new URL(m,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null}),y.lazyData.then(async n=>{let{flightData:r,canonicalUrl:c}=n;if("string"==typeof r)return(0,s.handleExternalUrl)(e,p,r,e.pushRef.pendingPush);for(let n of(y.lazyData=null,r)){let{tree:r,seedData:l,head:h,isRootRender:b}=n;if(!b)return console.log("REFRESH FAILED"),e;let x=(0,o.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===x)return(0,d.handleSegmentMismatch)(e,t,r);if((0,a.isNavigatingToNewRootLayout)(g,x))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let P=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=P),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(y,void 0,r,l,h,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({state:e,updatedTree:x,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=x,g=x}return(0,l.handleMutable)(e,p)},()=>e)}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8876:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},9289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},9384:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r)}return i}(e))&&(r&&(r+=" "),r+=t);return r}},9435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let r=n(642);function i(e){return void 0!==e}function o(e,t){var n,o;let a=null==(n=t.shouldScroll)||n,s=e.nextUrl;if(i(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?s=n:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(o=null==t?void 0:t.scrollableSegments)?o:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let r=n(7391),i=n(6770),o=n(2030),a=n(5232),s=n(6928),l=n(9435),u=n(9752);function c(e,t){let{serverResponse:{flightData:n,canonicalUrl:c}}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof n)return(0,a.handleExternalUrl)(e,d,n,e.pushRef.pendingPush);let h=e.tree,f=e.cache;for(let t of n){let{segmentPath:n,tree:l}=t,p=(0,i.applyRouterStatePatchToTree)(["",...n],h,l,e.canonicalUrl);if(null===p)return e;if((0,o.isNavigatingToNewRootLayout)(h,p))return(0,a.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let m=c?(0,r.createHrefFromUrl)(c):void 0;m&&(d.canonicalUrl=m);let g=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(f,g,t),d.patchedTree=p,d.cache=g,f=g,h=p}return(0,l.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9656:(e,t,n)=>{n.r(t),n.d(t,{_:()=>i});var r=0;function i(e){return"__private_"+r+++"_"+e}},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let r=n(3913),i=n(9752),o=n(6770),a=n(7391),s=n(3123),l=n(3898),u=n(9435);function c(e,t,n,c){let h,f=e.tree,p=e.cache,m=(0,a.createHrefFromUrl)(n);if("string"==typeof t)return!1;for(let e of t){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(e.seedData))continue;let t=e.tree;t=d(t,Object.fromEntries(n.searchParams));let{seedData:a,isRootRender:u,pathToSegment:c}=e,g=["",...c];t=d(t,Object.fromEntries(n.searchParams));let y=(0,o.applyRouterStatePatchToTree)(g,f,t,m),v=(0,i.createEmptyCacheNode)();if(u&&a){let e=a[1];v.loading=a[3],v.rsc=e,function e(t,n,i,o){if(0!==Object.keys(i[1]).length)for(let a in i[1]){let l;let u=i[1][a],c=u[0],d=(0,s.createRouterCacheKey)(c),h=null!==o&&void 0!==o[2][a]?o[2][a]:null;if(null!==h){let e=h[1],t=h[3];l={lazyData:null,rsc:c.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:t}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null};let f=t.parallelRoutes.get(a);f?f.set(d,l):t.parallelRoutes.set(a,new Map([[d,l]])),e(l,n,u,h)}}(v,p,t,a)}else v.rsc=p.rsc,v.prefetchRsc=p.prefetchRsc,v.loading=p.loading,v.parallelRoutes=new Map(p.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(v,p,e);y&&(f=y,p=v,h=!0)}return!!h&&(c.patchedTree=f,c.cache=p,c.canonicalUrl=m,c.hashFragment=n.hash,(0,u.handleMutable)(e,c))}function d(e,t){let[n,i,...o]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),i,...o];let a={};for(let[e,n]of Object.entries(i))a[e]=d(n,t);return[n,a,...o]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return A},createPrefetchURL:function(){return _},default:function(){return D}});let r=n(740),i=n(687),o=r._(n(3210)),a=n(2142),s=n(9154),l=n(7391),u=n(449),c=n(4545),d=r._(n(5656)),h=n(5416),f=n(6127),p=n(7022),m=n(7086),g=n(4397),y=n(9330),v=n(5942),b=n(6736),x=n(642),P=n(2776),w=n(1264);n(593);let T=n(6875),R=n(7860),S=n(5076);n(3406);let E={};function M(e){return e.origin!==window.location.origin}function _(e){let t;if((0,h.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return M(t)?null:t}function j(e){let{appRouterState:t}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(i,"",r)):window.history.replaceState(i,"",r)},[t]),(0,o.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function A(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null}}function C(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function O(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,i=null!==r?r:n;return(0,o.useDeferredValue)(n,i)}function k(e){let t,{actionQueue:n,assetPrefix:r,globalError:l}=e,[h,P]=(0,c.useReducer)(n),{canonicalUrl:A}=(0,c.useUnwrapState)(h),{searchParams:k,pathname:D}=(0,o.useMemo)(()=>{let e=new URL(A,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[A]),L=(0,o.useCallback)(e=>{let{previousTree:t,serverResponse:n}=e;(0,o.startTransition)(()=>{P({type:s.ACTION_SERVER_PATCH,previousTree:t,serverResponse:n})})},[P]),V=(0,o.useCallback)((e,t,n)=>{let r=new URL((0,f.addBasePath)(e),location.href);return P({type:s.ACTION_NAVIGATE,url:r,isExternalUrl:M(r),locationSearch:location.search,shouldScroll:null==n||n,navigateType:t,allowAliasing:!0})},[P]);(0,w.useServerActionDispatcher)(P);let U=(0,o.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=_(e);if(null!==r){var i;(0,S.prefetchReducer)(n.state,{type:s.ACTION_PREFETCH,url:r,kind:null!=(i=null==t?void 0:t.kind)?i:s.PrefetchKind.FULL})}},replace:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var n;V(e,"replace",null==(n=t.scroll)||n)})},push:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var n;V(e,"push",null==(n=t.scroll)||n)})},refresh:()=>{(0,o.startTransition)(()=>{P({type:s.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}}),[n,P,V]);(0,o.useEffect)(()=>{window.next&&(window.next.router=U)},[U]),(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(E.pendingMpaPath=void 0,P({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[P]),(0,o.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,R.isRedirectError)(t)){e.preventDefault();let n=(0,T.getURLFromRedirectError)(t);(0,T.getRedirectTypeFromError)(t)===R.RedirectType.push?U.push(n,{}):U.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[U]);let{pushRef:F}=(0,c.useUnwrapState)(h);if(F.mpaNavigation){if(E.pendingMpaPath!==A){let e=window.location;F.pendingPush?e.assign(A):e.replace(A),E.pendingMpaPath=A}(0,o.use)(y.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{P({type:s.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=C(t),i&&n(i)),e(t,r,i)},window.history.replaceState=function(e,r,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=C(e),i&&n(i)),t(e,r,i)};let r=e=>{if(e.state){if(!e.state.__NA){window.location.reload();return}(0,o.startTransition)(()=>{P({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:e.state.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[P]);let{cache:I,tree:B,nextUrl:z,focusAndScrollRef:H}=(0,c.useUnwrapState)(h),W=(0,o.useMemo)(()=>(0,g.findHeadInCache)(I,B[1]),[I,B]),K=(0,o.useMemo)(()=>(0,x.getSelectedParams)(B),[B]),$=(0,o.useMemo)(()=>({parentTree:B,parentCacheNode:I,parentSegmentPath:null,url:A}),[B,I,A]),G=(0,o.useMemo)(()=>({changeByServerResponse:L,tree:B,focusAndScrollRef:H,nextUrl:z}),[L,B,H,z]);if(null!==W){let[e,n]=W;t=(0,i.jsx)(O,{headCacheNode:e},n)}else t=null;let Y=(0,i.jsxs)(m.RedirectBoundary,{children:[t,I.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:B})]});return Y=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:Y}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(j,{appRouterState:(0,c.useUnwrapState)(h)}),(0,i.jsx)(N,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:K,children:(0,i.jsx)(u.PathnameContext.Provider,{value:D,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:k,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:G,children:(0,i.jsx)(a.AppRouterContext.Provider,{value:U,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:$,children:Y})})})})})})]})}function D(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:o}=e;return(0,P.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(k,{actionQueue:t,assetPrefix:o,globalError:[n,r]})})}let L=new Set,V=new Set;function N(){let[,e]=o.default.useState(0),t=L.size;return(0,o.useEffect)(()=>{let n=()=>e(e=>e+1);return V.add(n),t!==L.size&&n(),()=>{V.delete(n)}},[t,e]),[...L].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&V.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};