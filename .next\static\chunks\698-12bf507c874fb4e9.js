"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{2085:(t,e,i)=>{i.d(e,{F:()=>o});var n=i(2596);let r=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,s=n.$,o=(t,e)=>i=>{var n;if((null==e?void 0:e.variants)==null)return s(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:o,defaultVariants:a}=e,l=Object.keys(o).map(t=>{let e=null==i?void 0:i[t],n=null==a?void 0:a[t];if(null===e)return null;let s=r(e)||r(n);return o[t][s]}),u=i&&Object.entries(i).reduce((t,e)=>{let[i,n]=e;return void 0===n||(t[i]=n),t},{});return s(t,l,null==e?void 0:null===(n=e.compoundVariants)||void 0===n?void 0:n.reduce((t,e)=>{let{class:i,className:n,...r}=e;return Object.entries(r).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...a,...u}[e]):({...a,...u})[e]===i})?[...t,i,n]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},2138:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2596:(t,e,i)=>{i.d(e,{$:()=>n});function n(){for(var t,e,i=0,n="",r=arguments.length;i<r;i++)(t=arguments[i])&&(e=function t(e){var i,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(n=t(e[i]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n)}return r}(t))&&(n&&(n+=" "),n+=e);return n}},2757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=i(6966)._(i(8859)),r=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||r.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},2894:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2900:(t,e,i)=>{let n;i.d(e,{P:()=>sS});var r=i(2115);let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(s),a=t=>180*t/Math.PI,l=t=>h(a(Math.atan2(t[1],t[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},h=t=>((t%=360)<0&&(t+=360),t),d=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),c=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:c,scale:t=>(d(t)+c(t))/2,rotateX:t=>h(a(Math.atan2(t[6],t[5]))),rotateY:t=>h(a(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let i,n;if(!t||"none"===t)return m(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=p,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=u,n=e}if(!n)return m(e);let s=i[e],o=n[1].split(",").map(y);return"function"==typeof s?s(o):o[s]}let g=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)};function y(t){return parseFloat(t.trim())}let v=t=>e=>"string"==typeof e&&e.startsWith(t),b=v("--"),x=v("var(--"),w=t=>!!x(t)&&T.test(t.split("/*")[0].trim()),T=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function P({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}let S=(t,e,i)=>t+(e-t)*i;function A(t){return void 0===t||1===t}function k({scale:t,scaleX:e,scaleY:i}){return!A(t)||!A(e)||!A(i)}function M(t){return k(t)||E(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function E(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function C(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function V(t,e=0,i=1,n,r){t.min=C(t.min,e,i,n,r),t.max=C(t.max,e,i,n,r)}function D(t,{x:e,y:i}){V(t.x,e.translate,e.scale,e.originPoint),V(t.y,i.translate,i.scale,i.originPoint)}function j(t,e){t.min=t.min+e,t.max=t.max+e}function R(t,e,i,n,r=.5){let s=S(t.min,t.max,r);V(t,e,i,s,n)}function L(t,e){R(t.x,e.x,e.scaleX,e.scale,e.originX),R(t.y,e.y,e.scaleY,e.scale,e.originY)}function O(t,e){return P(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let F=new Set(["width","height","top","left","right","bottom",...s]),B=(t,e,i)=>i>e?e:i<t?t:i,I={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},N={...I,transform:t=>B(0,1,t)},U={...I,default:1},z=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),$=z("deg"),W=z("%"),_=z("px"),Y=z("vh"),X=z("vw"),H={...W,parse:t=>W.parse(t)/100,transform:t=>W.transform(100*t)},K=t=>e=>e.test(t),G=[I,_,W,$,X,Y,{test:t=>"auto"===t,parse:t=>t}],q=t=>G.find(K(t)),Z=()=>{},Q=()=>{},J=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),tt=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,te=t=>t===I||t===_,ti=new Set(["x","y","z"]),tn=s.filter(t=>!ti.has(t)),tr={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};tr.translateX=tr.x,tr.translateY=tr.y;let ts=t=>t,to={},ta=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],tl={value:null,addProjectionMetrics:null};function tu(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=ta.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&tl.value&&tl.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:p,postRender:m}=o,f=()=>{let s=to.useManualTiming?r.timestamp:performance.now();i=!1,to.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),h.process(r),d.process(r),c.process(r),p.process(r),m.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(f))},g=()=>{i=!0,n=!0,r.isProcessing||t(f)};return{schedule:ta.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||g(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<ta.length;e++)o[ta[e]].cancel(t)},state:r,steps:o}}let{schedule:th,cancel:td,state:tc,steps:tp}=tu("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ts,!0),tm=new Set,tf=!1,tg=!1,ty=!1;function tv(){if(tg){let t=Array.from(tm).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tn.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tg=!1,tf=!1,tm.forEach(t=>t.complete(ty)),tm.clear()}function tb(){tm.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tg=!0)})}class tx{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tm.add(this),tf||(tf=!0,th.read(tb),th.resolveKeyframes(tv))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tm.delete(this)}cancel(){"scheduled"===this.state&&(tm.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tw=t=>/^0[^.\s]+$/u.test(t),tT=t=>Math.round(1e5*t)/1e5,tP=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tS=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tA=(t,e)=>i=>!!("string"==typeof i&&tS.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tk=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(tP);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tM=t=>B(0,255,t),tE={...I,transform:t=>Math.round(tM(t))},tC={test:tA("rgb","red"),parse:tk("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tE.transform(t)+", "+tE.transform(e)+", "+tE.transform(i)+", "+tT(N.transform(n))+")"},tV={test:tA("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tC.transform},tD={test:tA("hsl","hue"),parse:tk("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+W.transform(tT(e))+", "+W.transform(tT(i))+", "+tT(N.transform(n))+")"},tj={test:t=>tC.test(t)||tV.test(t)||tD.test(t),parse:t=>tC.test(t)?tC.parse(t):tD.test(t)?tD.parse(t):tV.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tC.transform(t):tD.transform(t),getAnimatableNone:t=>{let e=tj.parse(t);return e.alpha=0,tj.transform(e)}},tR=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tL="number",tO="color",tF=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tB(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tF,t=>(tj.test(t)?(n.color.push(s),r.push(tO),i.push(tj.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tL),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tI(t){return tB(t).values}function tN(t){let{split:e,types:i}=tB(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tL?r+=tT(t[s]):e===tO?r+=tj.transform(t[s]):r+=t[s]}return r}}let tU=t=>"number"==typeof t?0:tj.test(t)?tj.getAnimatableNone(t):t,tz={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tP)?.length||0)+(t.match(tR)?.length||0)>0},parse:tI,createTransformer:tN,getAnimatableNone:function(t){let e=tI(t);return tN(t)(e.map(tU))}},t$=new Set(["brightness","contrast","saturate","opacity"]);function tW(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tP)||[];if(!n)return t;let r=i.replace(n,""),s=+!!t$.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let t_=/\b([a-z-]*)\(.*?\)/gu,tY={...tz,getAnimatableNone:t=>{let e=t.match(t_);return e?e.map(tW).join(" "):t}},tX={...I,transform:Math.round},tH={borderWidth:_,borderTopWidth:_,borderRightWidth:_,borderBottomWidth:_,borderLeftWidth:_,borderRadius:_,radius:_,borderTopLeftRadius:_,borderTopRightRadius:_,borderBottomRightRadius:_,borderBottomLeftRadius:_,width:_,maxWidth:_,height:_,maxHeight:_,top:_,right:_,bottom:_,left:_,padding:_,paddingTop:_,paddingRight:_,paddingBottom:_,paddingLeft:_,margin:_,marginTop:_,marginRight:_,marginBottom:_,marginLeft:_,backgroundPositionX:_,backgroundPositionY:_,rotate:$,rotateX:$,rotateY:$,rotateZ:$,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:$,skewX:$,skewY:$,distance:_,translateX:_,translateY:_,translateZ:_,x:_,y:_,z:_,perspective:_,transformPerspective:_,opacity:N,originX:H,originY:H,originZ:_,zIndex:tX,fillOpacity:N,strokeOpacity:N,numOctaves:tX},tK={...tH,color:tj,backgroundColor:tj,outlineColor:tj,fill:tj,stroke:tj,borderColor:tj,borderTopColor:tj,borderRightColor:tj,borderBottomColor:tj,borderLeftColor:tj,filter:tY,WebkitFilter:tY},tG=t=>tK[t];function tq(t,e){let i=tG(t);return i!==tY&&(i=tz),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tZ=new Set(["auto","none","0"]);class tQ extends tx{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&w(n=n.trim())){let r=function t(e,i,n=1){Q(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[r,s]=function(t){let e=tt.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return J(t)?parseFloat(t):t}return w(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!F.has(i)||2!==t.length)return;let[n,r]=t,s=q(n),o=q(r);if(s!==o){if(te(s)&&te(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tr[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tw(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!tZ.has(e)&&tB(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=tq(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=tr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tJ=t=>!!(t&&t.getVelocity);function t0(){n=void 0}let t1={now:()=>(void 0===n&&t1.set(tc.isProcessing||to.useManualTiming?tc.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(t0)}};function t2(t,e){-1===t.indexOf(e)&&t.push(e)}function t5(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class t3{constructor(){this.subscriptions=[]}add(t){return t2(this.subscriptions,t),()=>t5(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t9=t=>!isNaN(parseFloat(t)),t4={current:void 0};class t6{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=t1.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=t1.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=t9(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t3);let i=this.events[t].add(e);return"change"===t?()=>{i(),th.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t4.current&&t4.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=t1.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t8(t,e){return new t6(t,e)}let t7=[...G,tj,tz],et=t=>t7.find(K(t)),{schedule:ee}=tu(queueMicrotask,!1),ei={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},en={};for(let t in ei)en[t]={isEnabled:e=>ei[t].some(t=>!!e[t])};let er=()=>({translate:0,scale:1,origin:0,originPoint:0}),es=()=>({x:er(),y:er()}),eo=()=>({min:0,max:0}),ea=()=>({x:eo(),y:eo()}),el="undefined"!=typeof window,eu={current:null},eh={current:!1},ed=new WeakMap;function ec(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function ep(t){return"string"==typeof t||Array.isArray(t)}let em=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ef=["initial",...em];function eg(t){return ec(t.animate)||ef.some(e=>ep(t[e]))}function ey(t){return!!(eg(t)||t.variants)}function ev(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function eb(t,e,i,n){if("function"==typeof e){let[r,s]=ev(n);e=e(void 0!==i?i:t.custom,r,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,s]=ev(n);e=e(void 0!==i?i:t.custom,r,s)}return e}let ex=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ew{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tx,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=t1.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,th.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=eg(e),this.isVariantNode=ey(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&tJ(e)&&e.set(a[t])}}mount(t){this.current=t,ed.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eh.current||function(){if(eh.current=!0,el){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>eu.current=t.matches;t.addEventListener("change",e),e()}else eu.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||eu.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),td(this.notifyUpdate),td(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=o.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&th.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in en){let e=en[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ea()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ex.length;e++){let i=ex[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(tJ(r))t.addValue(n,r);else if(tJ(s))t.addValue(n,t8(r,{owner:t}));else if(s!==r){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,t8(void 0!==e?e:r,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=t8(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(J(i)||tw(i))?i=parseFloat(i):!et(i)&&tz.test(e)&&(i=tq(t,e)),this.setBaseTarget(t,tJ(i)?i.get():i)),tJ(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=eb(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tJ(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new t3),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){ee.render(this.render)}}class eT extends ew{constructor(){super(...arguments),this.KeyframeResolver=tQ}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tJ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let eP=(t,e)=>e&&"number"==typeof t?e.transform(t):t,eS={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},eA=s.length;function ek(t,e,i){let{style:n,vars:r,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let i=e[t];if(o.has(t)){l=!0;continue}if(b(t)){r[t]=i;continue}{let e=eP(i,tH[t]);t.startsWith("origin")?(u=!0,a[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=function(t,e,i){let n="",r=!0;for(let o=0;o<eA;o++){let a=s[o],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||i){let t=eP(l,tH[a]);if(!u){r=!1;let e=eS[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;n.transformOrigin=`${t} ${e} ${i}`}}function eM(t,{style:e,vars:i},n,r){let s;let o=t.style;for(s in e)o[s]=e[s];for(s in r?.applyProjectionStyles(o,n),i)o.setProperty(s,i[s])}let eE={};function eC(t,{layout:e,layoutId:i}){return o.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eE[t]||"opacity"===t)}function eV(t,e,i){let{style:n}=t,r={};for(let s in n)(tJ(n[s])||e.style&&tJ(e.style[s])||eC(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}class eD extends eT{constructor(){super(...arguments),this.type="html",this.renderInstance=eM}readValueFromInstance(t,e){if(o.has(e))return this.projection?.isProjecting?m(e):g(t,e);{let i=window.getComputedStyle(t),n=(b(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return O(t,e)}build(t,e,i){ek(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eV(t,e,i)}}let ej=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eR={offset:"stroke-dashoffset",array:"stroke-dasharray"},eL={offset:"strokeDashoffset",array:"strokeDasharray"};function eO(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(ek(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?eR:eL;t[s.offset]=_.transform(-n);let o=_.transform(e),a=_.transform(i);t[s.array]=`${o} ${a}`}(d,r,s,o,!1)}let eF=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),eB=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eI(t,e,i){let n=eV(t,e,i);for(let i in t)(tJ(t[i])||tJ(e[i]))&&(n[-1!==s.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class eN extends eT{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ea}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(o.has(e)){let t=tG(e);return t&&t.default||0}return e=eF.has(e)?e:ej(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eI(t,e,i)}build(t,e,i){eO(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){!function(t,e,i,n){for(let i in eM(t,e,void 0,n),e.attrs)t.setAttribute(eF.has(i)?i:ej(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=eB(t.tagName),super.mount(t)}}let eU=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ez(t){if("string"!=typeof t||t.includes("-"));else if(eU.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var e$=i(5155);let eW=(0,r.createContext)({}),e_=(0,r.createContext)({strict:!1}),eY=(0,r.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),eX=(0,r.createContext)({});function eH(t){return Array.isArray(t)?t.join(" "):t}let eK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eG(t,e,i){for(let n in e)tJ(e[n])||eC(n,i)||(t[n]=e[n])}let eq=()=>({...eK(),attrs:{}}),eZ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eQ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eZ.has(t)}let eJ=t=>!eQ(t);try{!function(t){"function"==typeof t&&(eJ=e=>e.startsWith("on")?!eQ(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let e0=(0,r.createContext)(null);function e1(t){return tJ(t)?t.get():t}let e2=t=>(e,i)=>{let n=(0,r.useContext)(eX),s=(0,r.useContext)(e0),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,r){return{latestValues:function(t,e,i,n){let r={},s=n(t,{});for(let t in s)r[t]=e1(s[t]);let{initial:o,animate:a}=t,l=eg(t),u=ey(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,d=(h=h||!1===o)?a:o;if(d&&"boolean"!=typeof d&&!ec(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=eb(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,n,r,t),renderState:e()}})(t,e,n,s);return i?o():function(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}(o)},e5=e2({scrapeMotionValuesFromProps:eV,createRenderState:eK}),e3=e2({scrapeMotionValuesFromProps:eI,createRenderState:eq}),e9=Symbol.for("motionComponentSymbol");function e4(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e6="data-"+ej("framerAppearId"),e8=(0,r.createContext)({}),e7=el?r.useLayoutEffect:r.useEffect;function it(t){var e,i;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(t){for(let e in t)en[e]={...en[e],...t[e]}}(s);let a=ez(t)?e3:e5;function l(e,i){var s,l,u;let h;let d={...(0,r.useContext)(eY),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,r.useContext)(eW).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(eg(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ep(e)?e:void 0,animate:ep(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,r.useContext)(eX));return(0,r.useMemo)(()=>({initial:e,animate:i}),[eH(e),eH(i)])}(e),m=a(e,c);if(!c&&el){l=0,u=0,(0,r.useContext)(e_).strict;let e=function(t){let{drag:e,layout:i}=en;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(d);h=e.MeasureLayout,p.visualElement=function(t,e,i,n,s){let{visualElement:o}=(0,r.useContext)(eX),a=(0,r.useContext)(e_),l=(0,r.useContext)(e0),u=(0,r.useContext)(eY).reducedMotion,h=(0,r.useRef)(null);n=n||a.renderer,!h.current&&n&&(h.current=n(t,{visualState:e,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=h.current,c=(0,r.useContext)(e8);d&&!d.projection&&s&&("html"===d.type||"svg"===d.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&e4(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,s,c);let p=(0,r.useRef)(!1);(0,r.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[e6],f=(0,r.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return e7(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,r.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1),d.enteringChildren=void 0)}),d}(t,m,d,o,e.ProjectionNode)}return(0,e$.jsxs)(eX.Provider,{value:p,children:[h&&p.visualElement?(0,e$.jsx)(h,{visualElement:p.visualElement,...d}):null,function(t,e,i,{latestValues:n},s,o=!1){let a=(ez(t)?function(t,e,i,n){let s=(0,r.useMemo)(()=>{let i=eq();return eO(i,e,eB(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};eG(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return eG(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,r.useMemo)(()=>{let i=eK();return ek(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(e,n,s,t),l=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(eJ(r)||!0===i&&eQ(r)||!e&&!eQ(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(e,"string"==typeof t,o),u=t!==r.Fragment?{...l,...a,ref:i}:{},{children:h}=e,d=(0,r.useMemo)(()=>tJ(h)?h.get():h,[h]);return(0,r.createElement)(t,{...u,children:d})}(t,e,(s=p.visualElement,(0,r.useCallback)(t=>{t&&m.onMount&&m.onMount(t),s&&(t?s.mount(t):s.unmount()),i&&("function"==typeof i?i(t):e4(i)&&(i.current=t))},[s])),m,c,n)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!==(i=null!==(e=t.displayName)&&void 0!==e?e:t.name)&&void 0!==i?i:"",")"));let u=(0,r.forwardRef)(l);return u[e9]=t,u}function ie(t,e,i){let n=t.getProps();return eb(n,e,void 0!==i?i:n.custom,t)}function ii(t,e){return t?.[e]??t?.default??t}let ir=t=>Array.isArray(t);function is(t,e){let i=t.getValue("willChange");if(tJ(i)&&i.add)return i.add(e);if(!i&&to.WillChange){let i=new to.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function io(t){t.duration=0,t.type}let ia=(t,e)=>i=>e(t(i)),il=(...t)=>t.reduce(ia),iu=t=>1e3*t,ih=t=>t/1e3,id={layout:0,mainThread:0,waapi:0};function ic(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ip(t,e){return i=>i>0?e:t}let im=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},ig=[tV,tC,tD],iy=t=>ig.find(e=>e.test(t));function iv(t){let e=iy(t);if(Z(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tD&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=ic(a,n,t+1/3),s=ic(a,n,t),o=ic(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let ib=(t,e)=>{let i=iv(t),n=iv(e);if(!i||!n)return ip(t,e);let r={...i};return t=>(r.red=im(i.red,n.red,t),r.green=im(i.green,n.green,t),r.blue=im(i.blue,n.blue,t),r.alpha=S(i.alpha,n.alpha,t),tC.transform(r))},ix=new Set(["none","hidden"]);function iw(t,e){return i=>S(t,e,i)}function iT(t){return"number"==typeof t?iw:"string"==typeof t?w(t)?ip:tj.test(t)?ib:iA:Array.isArray(t)?iP:"object"==typeof t?tj.test(t)?ib:iS:ip}function iP(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>iT(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function iS(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=iT(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let iA=(t,e)=>{let i=tz.createTransformer(e),n=tB(t),r=tB(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?ix.has(t)&&!r.values.length||ix.has(e)&&!n.values.length?function(t,e){return ix.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):il(iP(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(Z(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),ip(t,e))};function ik(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?S(t,e,i):iT(t)(t,e)}let iM=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>th.update(e,t),stop:()=>td(e),now:()=>tc.isProcessing?tc.timestamp:t1.now()}},iE=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function iC(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function iV(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let iD={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ij(t,e){return t*Math.sqrt(1-e*e)}let iR=["duration","bounce"],iL=["stiffness","damping","mass"];function iO(t,e){return e.some(e=>void 0!==t[e])}function iF(t=iD.visualDuration,e=iD.bounce){let i;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iD.velocity,stiffness:iD.stiffness,damping:iD.damping,mass:iD.mass,isResolvedFromDuration:!1,...t};if(!iO(t,iL)&&iO(t,iR)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:iD.mass,stiffness:n,damping:r}}else{let i=function({duration:t=iD.duration,bounce:e=iD.bounce,velocity:i=iD.velocity,mass:n=iD.mass}){let r,s;Z(t<=iu(iD.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=B(iD.minDamping,iD.maxDamping,o),t=B(iD.minDuration,iD.maxDuration,ih(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/ij(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=ij(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=iu(t),isNaN(a))return{stiffness:iD.stiffness,damping:iD.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:iD.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-ih(n.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=ih(Math.sqrt(u/d)),b=5>Math.abs(y);if(r||(r=b?iD.restSpeed.granular:iD.restSpeed.default),s||(s=b?iD.restDelta.granular:iD.restDelta.default),g<1){let t=ij(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let x={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;g<1&&(n=0===t?iu(f):iV(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(iC(x),2e4),e=iE(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function iB({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,b=void 0===o?v:o(v);b!==v&&(y=b-p);let x=t=>-y*Math.exp(-t/n),w=t=>b+x(t),T=t=>{let e=x(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:i},P=t=>{f(m.value)&&(d=t,c=iF({keyframes:[m.value,g(m.value)],velocity:iV(w,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}iF.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(iC(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:ih(r)}}(t,100,iF);return t.ease=e.ease,t.duration=iu(e.duration),t.type="keyframes",t};let iI=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function iN(t,e,i,n){if(t===e&&i===n)return ts;let r=e=>(function(t,e,i,n,r){let s,o;let a=0;do(s=iI(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:iI(r(t),e,n)}let iU=iN(.42,0,1,1),iz=iN(0,0,.58,1),i$=iN(.42,0,.58,1),iW=t=>Array.isArray(t)&&"number"!=typeof t[0],i_=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,iY=t=>e=>1-t(1-e),iX=iN(.33,1.53,.69,.99),iH=iY(iX),iK=i_(iH),iG=t=>(t*=2)<1?.5*iH(t):.5*(2-Math.pow(2,-10*(t-1))),iq=t=>1-Math.sin(Math.acos(t)),iZ=iY(iq),iQ=i_(iq),iJ=t=>Array.isArray(t)&&"number"==typeof t[0],i0={linear:ts,easeIn:iU,easeInOut:i$,easeOut:iz,circIn:iq,circInOut:iQ,circOut:iZ,backIn:iH,backInOut:iK,backOut:iX,anticipate:iG},i1=t=>"string"==typeof t,i2=t=>{if(iJ(t)){Q(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,r]=t;return iN(e,i,n,r)}return i1(t)?(Q(void 0!==i0[t],`Invalid easing type '${t}'`,"invalid-easing-type"),i0[t]):t},i5=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function i3({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=iW(n)?n.map(i2):i2(n),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(Q(s===e.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||to.mix||ik,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=il(Array.isArray(e)?e[i]||ts:e,s)),n.push(s)}return n}(e,n,r),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=i5(t[n],t[n+1],i);return a[n](r)};return i?e=>u(B(t[0],t[s-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=i5(0,e,n);t.push(S(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||i$).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let i9=t=>null!==t;function i4(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(i9),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let i6={decay:iB,inertia:iB,tween:i3,keyframes:i3,spring:iF};function i8(t){"string"==typeof t.type&&(t.type=i6[t.type])}class i7{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let nt=t=>t/100;class ne extends i7{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==t1.now()&&this.tick(t1.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},id.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;i8(t);let{type:e=i3,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||i3;a!==i3&&"number"!=typeof o[0]&&(this.mixKeyframes=il(nt,ik(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=iC(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,b=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(b=s)),v=B(0,1,i)*o}let x=y?{done:!1,value:u[0]}:b.next(v);r&&(x.value=r(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&p!==iB&&(x.value=i4(u,this.options,f,this.speed)),m&&m(x.value),T&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return ih(this.calculatedDuration)}get time(){return ih(this.currentTime)}set time(t){t=iu(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(t1.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=ih(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=iM,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(t1.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,id.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ni=t=>t.startsWith("--");function nn(t){let e;return()=>(void 0===e&&(e=t()),e)}let nr=nn(()=>void 0!==window.ScrollTimeline),ns={},no=function(t,e){let i=nn(t);return()=>ns[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),na=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,nl={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:na([0,.65,.55,1]),circOut:na([.55,0,1,.45]),backIn:na([.31,.01,.66,-.59]),backOut:na([.33,1.53,.69,.99])};function nu(t){return"function"==typeof t&&"applyToOptions"in t}class nh extends i7{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,Q("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return nu(t)&&no()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?no()?iE(e,i):"ease-out":iJ(e)?na(e):Array.isArray(e)?e.map(e=>t(e,i)||nl.easeOut):nl[e]}(a,r);Array.isArray(d)&&(h.easing=d),tl.value&&id.waapi++;let c={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return tl.value&&p.finished.finally(()=>{id.waapi--}),p}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=i4(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){ni(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return ih(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return ih(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=iu(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&nr())?(this.animation.timeline=t,ts):e(this)}}let nd={anticipate:iG,backInOut:iK,circInOut:iQ};class nc extends nh{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in nd&&(t.ease=nd[t.ease])}(t),i8(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new ne({...s,autoplay:!1}),a=iu(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let np=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tz.test(t)||"0"===t)&&!t.startsWith("url(")),nm=new Set(["opacity","clipPath","filter","transform"]),nf=nn(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ng extends i7{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=t1.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||tx;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=t1.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=np(r,e),a=np(s,e);return Z(o===a,`You are trying to animate ${e} from "${r}" to "${s}". "${o?s:r}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||nu(i))&&n)}(t,r,s,o)&&((to.instantAnimations||!a)&&u?.(i4(t,i,e)),t[0]=t[t.length-1],io(i),i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return nf()&&i&&nm.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(h)?new nc({...h,element:h.motionValue.owner.current}):new ne(h);d.finished.then(()=>this.notifyFinished()).catch(ts),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ty=!0,tb(),tv(),ty=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let ny=t=>null!==t,nv={type:"spring",stiffness:500,damping:25,restSpeed:10},nb=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),nx={type:"keyframes",duration:.8},nw={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nT=(t,{keyframes:e})=>e.length>2?nx:o.has(t)?t.startsWith("scale")?nb(e[1]):nv:nw,nP=(t,e,i,n={},r,s)=>o=>{let a=ii(n,t)||{},l=a.delay||n.delay||0,{elapsed:u=0}=n;u-=iu(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(h,nT(t,h)),h.duration&&(h.duration=iu(h.duration)),h.repeatDelay&&(h.repeatDelay=iu(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(io(h),0!==h.delay||(d=!0)),(to.instantAnimations||to.skipAnimations)&&(d=!0,io(h),h.delay=0),h.allowFlatten=!a.type&&!a.ease,d&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(ny),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(h.keyframes,a);if(void 0!==t){th.update(()=>{h.onUpdate(t),h.onComplete()});return}}return a.isSync?new ne(h):new ng(h)};function nS(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=e;n&&(s=n);let l=[],u=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){let n=t.getValue(e,t.latestValues[e]??null),r=a[e];if(void 0===r||u&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(u,e))continue;let o={delay:i,...ii(s||{},e)},h=n.get();if(void 0!==h&&!n.isAnimating&&!Array.isArray(r)&&r===h&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[e6];if(i){let t=window.MotionHandoffAnimation(i,e,th);null!==t&&(o.startTime=t,d=!0)}}is(t,e),n.start(nP(e,n,r,t.shouldReduceMotion&&F.has(e)?{type:!1}:o,t,d));let c=n.animation;c&&l.push(c)}return o&&Promise.all(l).then(()=>{th.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=ie(t,e)||{};for(let e in r={...r,...i}){var s;let i=ir(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,t8(i))}}(t,o)})}),l}function nA(t,e,i,n=0,r=1){let s=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),o=t.size,a=(o-1)*n;return"function"==typeof i?i(s,o):1===r?s*n:a-s*n}function nk(t,e,i={}){let n=ie(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(nS(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=0,s=1,o){let a=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),a.push(nk(l,e,{...o,delay:i+("function"==typeof n?0:n)+nA(t.variantChildren,l,n,r,s)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,n,s,o,a,i)}:()=>Promise.resolve(),{when:a}=r;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function nM(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let nE=ef.length,nC=[...em].reverse(),nV=em.length;function nD(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nj(){return{animate:nD(!0),whileInView:nD(),whileHover:nD(),whileTap:nD(),whileDrag:nD(),whileFocus:nD(),exit:nD()}}class nR{constructor(t){this.isMounted=!1,this.node=t}update(){}}class nL extends nR{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>nk(t,e,i)));else if("string"==typeof e)n=nk(t,e,i);else{let r="function"==typeof e?ie(t,e,i.custom):e;n=Promise.all(nS(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=nj(),n=!0,r=e=>(i,n)=>{let r=ie(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<nE;t++){let n=ef[t],r=e.props[n];(ep(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},l=[],u=new Set,h={},d=1/0;for(let e=0;e<nV;e++){var c,p;let m=nC[e],f=i[m],g=void 0!==o[m]?o[m]:a[m],y=ep(g),v=m===s?f.isActive:null;!1===v&&(d=e);let b=g===a[m]&&g!==o[m]&&y;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...h},!f.isActive&&null===v||!g&&!f.prevProp||ec(g)||"boolean"==typeof g)continue;let x=(c=f.prevProp,"string"==typeof(p=g)?p!==c:!!Array.isArray(p)&&!nM(p,c)),w=x||m===s&&f.isActive&&!b&&y||e>d&&y,T=!1,P=Array.isArray(g)?g:[g],S=P.reduce(r(m),{});!1===v&&(S={});let{prevResolvedValues:A={}}=f,k={...A,...S},M=e=>{w=!0,u.has(e)&&(T=!0,u.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in k){let e=S[t],i=A[t];if(h.hasOwnProperty(t))continue;let n=!1;(ir(e)&&ir(i)?nM(e,i):e===i)?void 0!==e&&u.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):u.add(t)}f.prevProp=g,f.prevResolvedValues=S,f.isActive&&(h={...h,...S}),n&&t.blockInitialAnimation&&(w=!1);let E=b&&x,C=!E||T;w&&C&&l.push(...P.map(e=>{let i={type:m};if("string"==typeof e&&n&&!E&&t.manuallyAnimateOnMount&&t.parent){let{parent:n}=t,r=ie(n,e);if(n.enteringChildren&&r){let{delayChildren:e}=r.transition||{};i.delay=nA(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(u.size){let e={};if("boolean"!=typeof o.initial){let i=ie(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let m=!!l.length;return n&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(m=!1),n=!1,m?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=s(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=nj(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();ec(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nO=0;class nF extends nR{constructor(){super(...arguments),this.id=nO++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let nB={x:!1,y:!1};function nI(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let nN=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function nU(t){return{point:{x:t.pageX,y:t.pageY}}}let nz=t=>e=>nN(e)&&t(e,nU(e));function n$(t,e,i,n){return nI(t,e,nz(i),n)}function nW(t){return t.max-t.min}function n_(t,e,i,n=.5){t.origin=n,t.originPoint=S(e.min,e.max,t.origin),t.scale=nW(i)/nW(e),t.translate=S(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function nY(t,e,i,n){n_(t.x,e.x,i.x,n?n.originX:void 0),n_(t.y,e.y,i.y,n?n.originY:void 0)}function nX(t,e,i){t.min=i.min+e.min,t.max=t.min+nW(e)}function nH(t,e,i){t.min=e.min-i.min,t.max=t.min+nW(e)}function nK(t,e,i){nH(t.x,e.x,i.x),nH(t.y,e.y,i.y)}function nG(t){return[t("x"),t("y")]}let nq=({current:t})=>t?t.ownerDocument.defaultView:null,nZ=(t,e)=>Math.abs(t-e);class nQ{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=n1(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(nZ(t.x,e.x)**2+nZ(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=tc;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=nJ(e,this.transformPagePoint),th.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=n1("pointercancel"===t.type?this.lastMoveEventInfo:nJ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!nN(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=nJ(nU(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=tc;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,n1(o,this.history)),this.removeListeners=il(n$(this.contextWindow,"pointermove",this.handlePointerMove),n$(this.contextWindow,"pointerup",this.handlePointerUp),n$(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),td(this.updatePoint)}}function nJ(t,e){return e?{point:e(t.point)}:t}function n0(t,e){return{x:t.x-e.x,y:t.y-e.y}}function n1({point:t},e){return{point:t,delta:n0(t,n2(e)),offset:n0(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=n2(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>iu(.1)));)i--;if(!n)return{x:0,y:0};let s=ih(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function n2(t){return t[t.length-1]}function n5(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function n3(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function n9(t,e,i){return{min:n4(t,e),max:n4(t,i)}}function n4(t,e){return"number"==typeof t?t:t[e]||0}let n6=new WeakMap;class n8{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ea(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nQ(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(nU(t).point)},onStart:(t,e)=>{var i;let{drag:n,dragPropagation:r,onDragStart:s}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=n)||"y"===i?nB[i]?null:(nB[i]=!0,()=>{nB[i]=!1}):nB.x||nB.y?null:(nB.x=nB.y=!0,()=>{nB.x=nB.y=!1}),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nG(t=>{let e=this.getAxisMotionValue(t).get()||0;if(W.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=nW(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&th.postRender(()=>s(t,e)),is(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>nG(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:nq(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&th.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!n7(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?S(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?S(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&e4(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:n5(t.x,i,r),y:n5(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:n9(t,"left","right"),y:n9(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nG(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!e4(e))return!1;let n=e.current;Q(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=O(t,i),{scroll:r}=e;return r&&(j(n.x,r.offset.x),j(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:n3((t=r.layout.layoutBox).x,s.x),y:n3(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=P(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(nG(o=>{if(!n7(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return is(this.visualElement,t),i.start(nP(t,i,0,e,this.visualElement,!1))}stopAnimation(){nG(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){nG(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){nG(e=>{let{drag:i}=this.getProps();if(!n7(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-S(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!e4(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};nG(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=nW(t),r=nW(e);return r>n?i=i5(e.min,e.max-n,t.min):n>r&&(i=i5(t.min,t.max-r,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nG(e=>{if(!n7(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(S(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;n6.set(this.visualElement,this);let t=n$(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e4(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),th.read(e);let r=nI(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(nG(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function n7(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rt extends nR{constructor(t){super(t),this.removeGroupControls=ts,this.removeListeners=ts,this.controls=new n8(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ts}unmount(){this.removeGroupControls(),this.removeListeners()}}let re=t=>(e,i)=>{t&&th.postRender(()=>t(e,i))};class ri extends nR{constructor(){super(...arguments),this.removePointerDownListener=ts}onPointerDown(t){this.session=new nQ(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nq(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:re(t),onStart:re(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&th.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=n$(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let rs={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!_.test(t))return t;t=parseFloat(t)}let i=rr(t,e.target.x),n=rr(t,e.target.y);return`${i}% ${n}%`}},ro=!1;class ra extends r.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;!function(t){for(let e in t)eE[e]=t[e],b(e)&&(eE[e].isCSSVariable=!0)}(ru),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),ro&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,ro=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||th.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ee.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;ro=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rl(t){let[e,i]=function(t=!0){let e=(0,r.useContext)(e0);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:s}=e,o=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return s(o)},[t]);let a=(0,r.useCallback)(()=>t&&n&&n(o),[o,n,t]);return!i&&n?[!1,a]:[!0]}(),n=(0,r.useContext)(eW);return(0,e$.jsx)(ra,{...t,layoutGroup:n,switchLayoutGroup:(0,r.useContext)(e8),isPresent:e,safeToRemove:i})}let ru={borderRadius:{...rs,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rs,borderTopRightRadius:rs,borderBottomLeftRadius:rs,borderBottomRightRadius:rs,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tz.parse(t);if(n.length>5)return t;let r=tz.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=S(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};function rh(t){return"object"==typeof t&&null!==t}function rd(t){return rh(t)&&"ownerSVGElement"in t}let rc=(t,e)=>t.depth-e.depth;class rp{constructor(){this.children=[],this.isDirty=!1}add(t){t2(this.children,t),this.isDirty=!0}remove(t){t5(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rc),this.isDirty=!1,this.children.forEach(t)}}let rm=["TopLeft","TopRight","BottomLeft","BottomRight"],rf=rm.length,rg=t=>"string"==typeof t?parseFloat(t):t,ry=t=>"number"==typeof t||_.test(t);function rv(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rb=rw(0,.5,iZ),rx=rw(.5,.95,ts);function rw(t,e,i){return n=>n<t?0:n>e?1:i(i5(t,e,n))}function rT(t,e){t.min=e.min,t.max=e.max}function rP(t,e){rT(t.x,e.x),rT(t.y,e.y)}function rS(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rA(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function rk(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(W.test(e)&&(e=parseFloat(e),e=S(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=S(s.min,s.max,n);t===s&&(a-=e),t.min=rA(t.min,e,i,a,r),t.max=rA(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let rM=["x","scaleX","originX"],rE=["y","scaleY","originY"];function rC(t,e,i,n){rk(t.x,e,rM,i?i.x:void 0,n?n.x:void 0),rk(t.y,e,rE,i?i.y:void 0,n?n.y:void 0)}function rV(t){return 0===t.translate&&1===t.scale}function rD(t){return rV(t.x)&&rV(t.y)}function rj(t,e){return t.min===e.min&&t.max===e.max}function rR(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rL(t,e){return rR(t.x,e.x)&&rR(t.y,e.y)}function rO(t){return nW(t.x)/nW(t.y)}function rF(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rB{constructor(){this.members=[]}add(t){t2(this.members,t),t.scheduleRender()}remove(t){if(t5(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rI={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rN=["","X","Y","Z"],rU=0;function rz(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function r$({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=rU++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tl.value&&(rI.nodes=rI.calculatedTargetDeltas=rI.calculatedProjections=0),this.nodes.forEach(rY),this.nodes.forEach(rQ),this.nodes.forEach(rJ),this.nodes.forEach(rX),tl.addProjectionMetrics&&tl.addProjectionMetrics(rI)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rp)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t3),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=rd(e)&&!(rd(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i;let n=0,r=()=>this.root.updateBlockedByResize=!1;th.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=t1.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(td(n),t(s-e))};return th.setup(n,!0),()=>td(n)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rZ)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||r9,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!rL(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...ii(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||rZ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),td(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r0),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[e6];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",th,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rK);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(rG);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(rq),this.nodes.forEach(rW),this.nodes.forEach(r_)):this.nodes.forEach(rG),this.clearAllSnapshots();let t=t1.now();tc.delta=B(0,1e3/60,t-tc.timestamp),tc.timestamp=t,tc.isProcessing=!0,tp.update.process(tc),tp.preRender.process(tc),tp.render.process(tc),tc.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ee.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rH),this.sharedNodes.forEach(r1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,th.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){th.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||nW(this.snapshot.measuredBox.x)||nW(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ea(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rD(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||M(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),r8((e=n).x),r8(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ea();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(st))){let{scroll:t}=this.root;t&&(j(e.x,t.offset.x),j(e.y,t.offset.y))}return e}removeElementScroll(t){let e=ea();if(rP(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&rP(e,t),j(e.x,r.offset.x),j(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=ea();rP(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&L(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),M(n.latestValues)&&L(i,n.latestValues)}return M(this.latestValues)&&L(i,this.latestValues),i}removeTransform(t){let e=ea();rP(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!M(i.latestValues))continue;k(i.latestValues)&&i.updateSnapshot();let n=ea();rP(n,i.measurePageBox()),rC(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return M(this.latestValues)&&rC(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tc.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=tc.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ea(),this.relativeTargetOrigin=ea(),nK(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ea(),this.targetWithTransforms=ea()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,nX(s.x,o.x,a.x),nX(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rP(this.target,this.layout.layoutBox),D(this.target,this.targetDelta)):rP(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ea(),this.relativeTargetOrigin=ea(),nK(this.relativeTargetOrigin,this.target,t.target),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tl.value&&rI.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||k(this.parent.latestValues)||E(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tc.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;rP(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&L(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,D(t,s)),n&&M(r.latestValues)&&L(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=ea());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rS(this.prevProjectionDelta.x,this.projectionDelta.x),rS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nY(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&rF(this.projectionDelta.x,this.prevProjectionDelta.x)&&rF(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),tl.value&&rI.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=es(),this.projectionDelta=es(),this.projectionDeltaWithTransform=es()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=es();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=ea(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(r3));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(r2(o.x,t.x,n),r2(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;if(nK(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=n,r5(p.x,m.x,f.x,g),r5(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,rj(u.x,c.x)&&rj(u.y,c.y)))this.isProjectionDirty=!1;i||(i=ea()),rP(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=S(0,i.opacity??1,rb(n)),t.opacityExit=S(e.opacity??1,0,rx(n))):s&&(t.opacity=S(e.opacity??1,i.opacity??1,n));for(let r=0;r<rf;r++){let s=`border${rm[r]}Radius`,o=rv(e,s),a=rv(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||ry(o)===ry(a)?(t[s]=Math.max(S(rg(o),rg(a),n),0),(W.test(a)||W.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=S(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(td(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=th.update(()=>{rn.hasAnimatedSinceResize=!0,id.layout++,this.motionValue||(this.motionValue=t8(0)),this.currentAnimation=function(t,e,i){let n=tJ(t)?t:t8(t);return n.start(nP("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{id.layout--},onComplete:()=>{id.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&r7(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||ea();let e=nW(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=nW(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}rP(e,i),L(e,r),nY(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rB),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&rz("z",t,n,this.animationValues);for(let e=0;e<rN.length;e++)rz(`rotate${rN[e]}`,t,n,this.animationValues),rz(`skew${rN[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=e1(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=e1(e?.pointerEvents)||""),this.hasProjected&&!M(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,eE){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=eE[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?e1(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(rK),this.root.sharedNodes.clear()}}}function rW(t){t.updateLayout()}function r_(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?nG(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=nW(n);n.min=i[t].min,n.max=n.min+r}):r7(r,e.layoutBox,i)&&nG(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=nW(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=es();nY(o,i,e.layoutBox);let a=es();s?nY(a,t.applyTransform(n,!0),e.measuredBox):nY(a,i,e.layoutBox);let l=!rD(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=ea();nK(o,e.layoutBox,r.layoutBox);let a=ea();nK(a,i,s.layoutBox),rL(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function rY(t){tl.value&&rI.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rX(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rH(t){t.clearSnapshot()}function rK(t){t.clearMeasurements()}function rG(t){t.isLayoutDirty=!1}function rq(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function rZ(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function rQ(t){t.resolveTargetDelta()}function rJ(t){t.calcProjection()}function r0(t){t.resetSkewAndRotation()}function r1(t){t.removeLeadSnapshot()}function r2(t,e,i){t.translate=S(e.translate,0,i),t.scale=S(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function r5(t,e,i,n){t.min=S(e.min,i.min,n),t.max=S(e.max,i.max,n)}function r3(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r9={duration:.45,ease:[.4,0,.1,1]},r4=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),r6=r4("applewebkit/")&&!r4("chrome/")?Math.round:ts;function r8(t){t.min=r6(t.min),t.max=r6(t.max)}function r7(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rO(e)-rO(i)))}function st(t){return t!==t.root&&t.scroll?.wasRoot}let se=r$({attachResizeListener:(t,e)=>nI(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),si={current:void 0},sn=r$({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!si.current){let t=new se({});t.mount(window),t.setOptions({layoutScroll:!0}),si.current=t}return si.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function sr(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function ss(t){return!("touch"===t.pointerType||nB.x||nB.y)}function so(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&th.postRender(()=>r(e,nU(e)))}class sa extends nR{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=sr(t,i),o=t=>{if(!ss(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{ss(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(so(this.node,e,"Start"),t=>so(this.node,t,"End"))))}unmount(){}}class sl extends nR{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=il(nI(this.node.current,"focus",()=>this.onFocus()),nI(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let su=(t,e)=>!!e&&(t===e||su(t,e.parentElement)),sh=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sd=new WeakSet;function sc(t){return e=>{"Enter"===e.key&&t(e)}}function sp(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let sm=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=sc(()=>{if(sd.has(i))return;sp(i,"down");let t=sc(()=>{sp(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sp(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function sf(t){return nN(t)&&!(nB.x||nB.y)}function sg(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&th.postRender(()=>r(e,nU(e)))}class sy extends nR{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=sr(t,i),o=t=>{let n=t.currentTarget;if(!sf(t))return;sd.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),sd.has(n)&&sd.delete(n),sf(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||su(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),rh(t)&&"offsetHeight"in t&&(t.addEventListener("focus",t=>sm(t,r)),!sh.has(t.tagName)&&-1===t.tabIndex&&!t.hasAttribute("tabindex")&&(t.tabIndex=0))}),s}(t,(t,e)=>(sg(this.node,e,"Start"),(t,{success:e})=>sg(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sv=new WeakMap,sb=new WeakMap,sx=t=>{let e=sv.get(t.target);e&&e(t)},sw=t=>{t.forEach(sx)},sT={some:0,all:1};class sP extends nR{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sT[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;sb.has(i)||sb.set(i,{});let n=sb.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(sw,{root:t,...e})),n[r]}(e);return sv.set(t,i),n.observe(t),()=>{sv.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sS=function(t,e){if("undefined"==typeof Proxy)return it;let i=new Map,n=(i,n)=>it(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(r,s)=>"create"===s?n:(i.has(s)||i.set(s,it(s,void 0,t,e)),i.get(s))})}({animation:{Feature:nL},exit:{Feature:nF},inView:{Feature:sP},tap:{Feature:sy},focus:{Feature:sl},hover:{Feature:sa},pan:{Feature:ri},drag:{Feature:rt,ProjectionNode:sn,MeasureLayout:rl},layout:{ProjectionNode:sn,MeasureLayout:rl}},(t,e)=>ez(t)?new eN(e):new eD(e,{allowProjection:t!==r.Fragment}))},3311:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},4783:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},6634:(t,e,i)=>{i.d(e,{DX:()=>a,TL:()=>o});var n=i(2115);function r(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}var s=i(5155);function o(t){let e=function(t){let e=n.forwardRef((t,e)=>{let{children:i,...s}=t;if(n.isValidElement(i)){var o;let t,a;let l=(o=i,(a=(t=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.ref:(a=(t=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(t,e){let i={...e};for(let n in e){let r=t[n],s=e[n];/^on[A-Z]/.test(n)?r&&s?i[n]=(...t)=>{let e=s(...t);return r(...t),e}:r&&(i[n]=r):"style"===n?i[n]={...r,...s}:"className"===n&&(i[n]=[r,s].filter(Boolean).join(" "))}return{...t,...i}}(s,i.props);return i.type!==n.Fragment&&(u.ref=e?function(...t){return e=>{let i=!1,n=t.map(t=>{let n=r(t,e);return i||"function"!=typeof n||(i=!0),n});if(i)return()=>{for(let e=0;e<n.length;e++){let i=n[e];"function"==typeof i?i():r(t[e],null)}}}}(e,l):l),n.cloneElement(i,u)}return n.Children.count(i)>1?n.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=n.forwardRef((t,i)=>{let{children:r,...o}=t,a=n.Children.toArray(r),l=a.find(u);if(l){let t=l.props.children,r=a.map(e=>e!==l?e:n.Children.count(t)>1?n.Children.only(null):n.isValidElement(t)?t.props.children:null);return(0,s.jsx)(e,{...o,ref:i,children:n.isValidElement(t)?n.cloneElement(t,void 0,r):null})}return(0,s.jsx)(e,{...o,ref:i,children:r})});return i.displayName=`${t}.Slot`,i}var a=o("Slot"),l=Symbol("radix.slottable");function u(t){return n.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(2115);function r(t,e){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=i.current;t&&(i.current=null,t());let e=r.current;e&&(r.current=null,e())}else t&&(i.current=s(t,n)),e&&(r.current=s(e,n))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return m}});let n=i(8229),r=i(5155),s=n._(i(2115)),o=i(2757),a=i(5227),l=i(9818),u=i(6654),h=i(9991),d=i(5929);i(3230);let c=i(4930);function p(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}let m=s.default.forwardRef(function(t,e){let i,n;let{href:o,as:m,children:f,prefetch:g=null,passHref:y,replace:v,shallow:b,scroll:x,onClick:w,onMouseEnter:T,onTouchStart:P,legacyBehavior:S=!1,...A}=t;i=f,S&&("string"==typeof i||"number"==typeof i)&&(i=(0,r.jsx)("a",{children:i}));let k=s.default.useContext(a.AppRouterContext),M=!1!==g,E=null===g?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:C,as:V}=s.default.useMemo(()=>{let t=p(o);return{href:t,as:m?p(m):t}},[o,m]);S&&(n=s.default.Children.only(i));let D=S?n&&"object"==typeof n&&n.ref:e,j=s.default.useCallback(t=>(M&&null!==k&&(0,c.mountLinkInstance)(t,C,k,E),()=>{(0,c.unmountLinkInstance)(t)}),[M,C,k,E]),R={ref:(0,u.useMergedRef)(j,D),onClick(t){S||"function"!=typeof w||w(t),S&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(t),k&&!t.defaultPrevented&&!function(t,e,i,n,r,o,a){let{nodeName:l}=t.currentTarget;!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t))&&(t.preventDefault(),s.default.startTransition(()=>{let t=null==a||a;"beforePopState"in e?e[r?"replace":"push"](i,n,{shallow:o,scroll:t}):e[r?"replace":"push"](n||i,{scroll:t})}))}(t,k,C,V,v,b,x)},onMouseEnter(t){S||"function"!=typeof T||T(t),S&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(t),k&&M&&(0,c.onNavigationIntent)(t.currentTarget)},onTouchStart:function(t){S||"function"!=typeof P||P(t),S&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(t),k&&M&&(0,c.onNavigationIntent)(t.currentTarget)}};return(0,h.isAbsoluteUrl)(V)?R.href=V:S&&!y&&("a"!==n.type||"href"in n.props)||(R.href=(0,d.addBasePath)(V)),S?s.default.cloneElement(n,R):(0,r.jsx)("a",{...A,...R,children:i})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},8175:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8859:(t,e)=>{function i(t){let e={};for(let[i,n]of t.entries()){let t=e[i];void 0===t?e[i]=n:Array.isArray(t)?t.push(n):e[i]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,n(t));else e.set(i,n(r));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,n]of e.entries())t.append(i,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},8883:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9099:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9688:(t,e,i)=>{i.d(e,{QP:()=>G});let n=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let r=i[t]||[];return e&&n[t]?[...r,...n[t]]:r}}},r=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],n=e.nextPart.get(i),s=n?r(t.slice(1),n):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,prefix:i}=t,n={nextPart:new Map,validators:[]};return d(Object.entries(t.classGroups),i).forEach(([t,i])=>{l(i,n,t,e)}),n},l=(t,e,i,n)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t){if(h(t)){l(t(n),e,i,n);return}e.validators.push({validator:t,classGroupId:i});return}Object.entries(t).forEach(([t,r])=>{l(r,u(e,t),i,n)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,d=(t,e)=>e?t.map(([t,i])=>[t,i.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,i])=>[e+t,i])):t)]):t,c=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,n=new Map,r=(r,s)=>{i.set(r,s),++e>t&&(e=0,n=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=n.get(t))?(r(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):r(t,e)}}},p=t=>{let{separator:e,experimentalParseClassName:i}=t,n=1===e.length,r=e[0],s=e.length,o=t=>{let i;let o=[],a=0,l=0;for(let u=0;u<t.length;u++){let h=t[u];if(0===a){if(h===r&&(n||t.slice(u,u+s)===e)){o.push(t.slice(l,u)),l=u+s;continue}if("/"===h){i=u;continue}}"["===h?a++:"]"===h&&a--}let u=0===o.length?t:t.substring(l),h=u.startsWith("!"),d=h?u.substring(1):u;return{modifiers:o,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:i&&i>l?i-l:void 0}};return i?t=>i({className:t,parseClassName:o}):o},m=t=>{if(t.length<=1)return t;let e=[],i=[];return t.forEach(t=>{"["===t[0]?(e.push(...i.sort(),t),i=[]):i.push(t)}),e.push(...i.sort()),e},f=t=>({cache:c(t.cacheSize),parseClassName:p(t),...n(t)}),g=/\s+/,y=(t,e)=>{let{parseClassName:i,getClassGroupId:n,getConflictingClassGroupIds:r}=e,s=[],o=t.trim().split(g),a="";for(let t=o.length-1;t>=0;t-=1){let e=o[t],{modifiers:l,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:d}=i(e),c=!!d,p=n(c?h.substring(0,d):h);if(!p){if(!c||!(p=n(h))){a=e+(a.length>0?" "+a:a);continue}c=!1}let f=m(l).join(":"),g=u?f+"!":f,y=g+p;if(s.includes(y))continue;s.push(y);let v=r(p,c);for(let t=0;t<v.length;++t){let e=v[t];s.push(g+e)}a=e+(a.length>0?" "+a:a)}return a};function v(){let t,e,i=0,n="";for(;i<arguments.length;)(t=arguments[i++])&&(e=b(t))&&(n&&(n+=" "),n+=e);return n}let b=t=>{let e;if("string"==typeof t)return t;let i="";for(let n=0;n<t.length;n++)t[n]&&(e=b(t[n]))&&(i&&(i+=" "),i+=e);return i},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,T=/^\d+\/\d+$/,P=new Set(["px","full","screen"]),S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=t=>D(t)||P.has(t)||T.test(t),V=t=>_(t,"length",Y),D=t=>!!t&&!Number.isNaN(Number(t)),j=t=>_(t,"number",D),R=t=>!!t&&Number.isInteger(Number(t)),L=t=>t.endsWith("%")&&D(t.slice(0,-1)),O=t=>w.test(t),F=t=>S.test(t),B=new Set(["length","size","percentage"]),I=t=>_(t,B,X),N=t=>_(t,"position",X),U=new Set(["image","url"]),z=t=>_(t,U,K),$=t=>_(t,"",H),W=()=>!0,_=(t,e,i)=>{let n=w.exec(t);return!!n&&(n[1]?"string"==typeof e?n[1]===e:e.has(n[1]):i(n[2]))},Y=t=>A.test(t)&&!k.test(t),X=()=>!1,H=t=>M.test(t),K=t=>E.test(t);Symbol.toStringTag;let G=function(t,...e){let i,n,r;let s=function(a){return n=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,r=i.cache.set,s=o,o(a)};function o(t){let e=n(t);if(e)return e;let s=y(t,i);return r(t,s),s}return function(){return s(v.apply(null,arguments))}}(()=>{let t=x("colors"),e=x("spacing"),i=x("blur"),n=x("brightness"),r=x("borderColor"),s=x("borderRadius"),o=x("borderSpacing"),a=x("borderWidth"),l=x("contrast"),u=x("grayscale"),h=x("hueRotate"),d=x("invert"),c=x("gap"),p=x("gradientColorStops"),m=x("gradientColorStopPositions"),f=x("inset"),g=x("margin"),y=x("opacity"),v=x("padding"),b=x("saturate"),w=x("scale"),T=x("sepia"),P=x("skew"),S=x("space"),A=x("translate"),k=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto",O,e],B=()=>[O,e],U=()=>["",C,V],_=()=>["auto",D,O],Y=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],X=()=>["solid","dashed","dotted","double","none"],H=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],K=()=>["start","end","center","between","around","evenly","stretch"],G=()=>["","0",O],q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[D,O];return{cacheSize:500,separator:":",theme:{colors:[W],spacing:[C,V],blur:["none","",F,O],brightness:Z(),borderColor:[t],borderRadius:["none","","full",F,O],borderSpacing:B(),borderWidth:U(),contrast:Z(),grayscale:G(),hueRotate:Z(),invert:G(),gap:B(),gradientColorStops:[t],gradientColorStopPositions:[L,V],inset:E(),margin:E(),opacity:Z(),padding:B(),saturate:Z(),scale:Z(),sepia:G(),skew:Z(),space:B(),translate:B()},classGroups:{aspect:[{aspect:["auto","square","video",O]}],container:["container"],columns:[{columns:[F]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Y(),O]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",R,O]}],basis:[{basis:E()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",O]}],grow:[{grow:G()}],shrink:[{shrink:G()}],order:[{order:["first","last","none",R,O]}],"grid-cols":[{"grid-cols":[W]}],"col-start-end":[{col:["auto",{span:["full",R,O]},O]}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":[W]}],"row-start-end":[{row:["auto",{span:[R,O]},O]}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",O]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",O]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...K()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...K(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...K(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",O,e]}],"min-w":[{"min-w":[O,e,"min","max","fit"]}],"max-w":[{"max-w":[O,e,"none","full","min","max","fit","prose",{screen:[F]},F]}],h:[{h:[O,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[O,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[O,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[O,e,"auto","min","max","fit"]}],"font-size":[{text:["base",F,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",j]}],"font-family":[{font:[W]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",O]}],"line-clamp":[{"line-clamp":["none",D,j]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",C,O]}],"list-image":[{"list-image":["none",O]}],"list-style-type":[{list:["none","disc","decimal",O]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...X(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",C,V]}],"underline-offset":[{"underline-offset":["auto",C,O]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:B()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Y(),N]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",I]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},z]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...X(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:X()}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["",...X()]}],"outline-offset":[{"outline-offset":[C,O]}],"outline-w":[{outline:[C,V]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[C,V]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",F,$]}],"shadow-color":[{shadow:[W]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...H(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":H()}],filter:[{filter:["","none"]}],blur:[{blur:[i]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",F,O]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[T]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[i]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",O]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",O]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",O]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[R,O]}],"translate-x":[{"translate-x":[A]}],"translate-y":[{"translate-y":[A]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",O]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",O]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":B()}],"scroll-mx":[{"scroll-mx":B()}],"scroll-my":[{"scroll-my":B()}],"scroll-ms":[{"scroll-ms":B()}],"scroll-me":[{"scroll-me":B()}],"scroll-mt":[{"scroll-mt":B()}],"scroll-mr":[{"scroll-mr":B()}],"scroll-mb":[{"scroll-mb":B()}],"scroll-ml":[{"scroll-ml":B()}],"scroll-p":[{"scroll-p":B()}],"scroll-px":[{"scroll-px":B()}],"scroll-py":[{"scroll-py":B()}],"scroll-ps":[{"scroll-ps":B()}],"scroll-pe":[{"scroll-pe":B()}],"scroll-pt":[{"scroll-pt":B()}],"scroll-pr":[{"scroll-pr":B()}],"scroll-pb":[{"scroll-pb":B()}],"scroll-pl":[{"scroll-pl":B()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",O]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[C,V,j]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},9946:(t,e,i)=>{i.d(e,{A:()=>l});var n=i(2115);let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:r=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:d,...c}=t;return(0,n.createElement)("svg",{ref:e,...o,width:r,height:r,stroke:i,strokeWidth:l?24*Number(a)/Number(r):a,className:s("lucide",u),...c},[...d.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(h)?h:[h]])}),l=(t,e)=>{let i=(0,n.forwardRef)((i,o)=>{let{className:l,...u}=i;return(0,n.createElement)(a,{ref:o,iconNode:e,className:s("lucide-".concat(r(t)),l),...u})});return i.displayName="".concat(t),i}},9991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return c},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return b}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>r.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let c="undefined"!=typeof performance,p=c&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class y extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);