const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

async function runLighthouse(url) {
  const chrome = await chromeLauncher.launch({chromeFlags: ['--headless']});
  const options = {
    logLevel: 'info',
    output: 'html',
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
    port: chrome.port,
  };
  
  const runnerResult = await lighthouse(url, options);

  // The gathered artifacts are typically removed as they can be quite large
  delete runnerResult.artifacts;

  const reportHtml = runnerResult.report;
  const reportPath = path.join(__dirname, '../lighthouse-report.html');
  fs.writeFileSync(reportPath, reportHtml);

  console.log('Report is done for', runnerResult.lhr.finalUrl);
  console.log('Report saved to', reportPath);
  
  // Log scores
  const scores = runnerResult.lhr.categories;
  console.log('\n=== Lighthouse Scores ===');
  console.log('Performance:', Math.round(scores.performance.score * 100));
  console.log('Accessibility:', Math.round(scores.accessibility.score * 100));
  console.log('Best Practices:', Math.round(scores['best-practices'].score * 100));
  console.log('SEO:', Math.round(scores.seo.score * 100));

  await chrome.kill();
  
  return runnerResult.lhr;
}

// Test multiple pages
async function testMultiplePages() {
  const baseUrl = 'http://localhost:3000';
  const pages = ['/', '/about', '/blog', '/contact'];
  
  console.log('Starting Lighthouse tests...\n');
  
  for (const page of pages) {
    const url = baseUrl + page;
    console.log(`\nTesting: ${url}`);
    try {
      await runLighthouse(url);
    } catch (error) {
      console.error(`Error testing ${url}:`, error.message);
    }
  }
}

// Run if called directly
if (require.main === module) {
  testMultiplePages().catch(console.error);
}

module.exports = { runLighthouse, testMultiplePages };
